/**
 * 课程表操作按钮
 */
export interface ActionButton {
  /** 按钮名称 */
  name: string
  /** 按钮图标 */
  icon: string
  /** 按钮链接 */
  url?: string
}

/**
 * 课程表项
 */
export interface ScheduleItem {
  /** 课程ID */
  id: number
  /** 教学任务ID */
  teachingTaskId: number
  /** 合并任务ID */
  mergeTaskId: string | number
  /** 上课方式名称 */
  skfsmc: string
  /** 辅导教师 */
  fdjs: string
  /** 课程名称 */
  course: string
  /** 课程代码 */
  courseCode: string | number
  /** 上课日期 */
  date: string
  /** 教师姓名 */
  teacherName: string
  /** 教室名称 */
  spaceName: string
  /** 班级名称 */
  className: string
  /** 校区名称 */
  xqmc: string
  /** 开始时间 */
  jckssj: string
  /** 结束时间 */
  jcjssj: string
  /** 学生人数 */
  studentCount: number
  /** 星期索引（1-7） */
  DayIndex: number
  /** 节次 */
  sections: string
  /** 显示节次 */
  sectionsShow: string
  /** 时间段（上午/下午/晚上） */
  section: string
  /** 开始节次 */
  startNode: string
  /** 结束节次 */
  endNode: string
  /** 周次 */
  week: number
  /** 课时数 */
  classHour: number
  /** 审批状态 */
  spzt: number
  /** 性质（正常/调课/补课等） */
  xz: string
  /** 操作按钮列表 */
  actionButtons: ActionButton[]
}

/**
 * 课程表响应
 */
export interface ScheduleResponse {
  /** 课程列表 */
  list: ScheduleItem[]
}

/**
 * 课程时间段项
 */
export interface ScheduleTimeItem {
  /** ID */
  id: number
  /** 时间类别，如"夏令制" */
  sjlb: string
  /** 节次代码 */
  jcdm: number
  /** 节次名称，如"第1节" */
  jcmc: string
  /** 节次上课开始时间 */
  jcskkssj: string
  /** 节次上课结束时间 */
  jcskjssj: string
  /** 备注，如"上午" */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 所属学期 */
  ssxq: string | null
  /** 所属学期名称 */
  ssxqmc: string | null
}

/**
 * 教师课程表相关类型定义
 */

/**
 * 教师课程表查询参数
 */
export interface TeacherScheduleQuery {
  /** 页码 */
  page?: number
  /** 每页条数 */
  pageSize?: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方式：asc|desc */
  sortOrder?: 'asc' | 'desc'
  /** 课程审批状态 */
  spzt?: number
  /** 上课日期范围 */
  skrq?: string[]
}

/**
 * 教师课程表项
 */
export interface TeacherScheduleItem {
  /** 课程表ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
  /** 周次 */
  zc: number
  /** 上课日期 */
  skrq: string
  /** 上课开始时间 */
  skkssj: string
  /** 上课结束时间 */
  skjssj: string
  /** 学期数 */
  xqs: number
  /** 节次 */
  jc: string
  /** 展示用节次 */
  jcshow: string
  /** 上课能力 */
  sknl: string
  /** 上课方式 */
  skfs: string
  /** 上课方式名称 */
  skfsmc: string | null
  /** 作业题数 */
  zyts: number
  /** 作业评估方式 */
  zypgfs: string
  /** 作业评估方式名称 */
  zypgfsmc: string | null
  /** 分组人数组 */
  fzrszs: string
  /** 使用仪器设备数量 */
  syyqsbsl: string
  /** 上课场地类型 */
  skcdlx: string
  /** 上课场地代码 */
  skcddm: string | null
  /** 上课场地名称 */
  skcdmc: string | null
  /** 上课教师编号 */
  skjs: string
  /** 上课教师姓名 */
  skjsxm: string
  /** 上课班级ID */
  skbj: string
  /** 上课班级名称 */
  skbjmc: string
  /** 实用人数 */
  syrs: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 课时 */
  ks: number
  /** 学制 */
  xz: number
  /** 关联ID */
  glid: number
  /** 课程中心状态 */
  skjhzxzt: number
  /** 工作量录入状态 */
  skjhgzlrdzt: number
  /** 作业内容 */
  zynr: string | null
  /** 教师确认状态 */
  skjhjsqrzt: number
  /** 教师确认时间 */
  skjhjsqrsj: string
  /** 教师确认人编号 */
  skjhjsqrrybh: string | null
  /** 教师确认人姓名 */
  skjhjsqrryxm: string | null
  /** 学生确认状态 */
  skjhxsqrzt: number
  /** 学生确认时间 */
  skjhxsqrsj: string
  /** 学生确认学号 */
  skjhxsqrxsxh: string | null
  /** 学生确认姓名 */
  skjhxsqrxsxm: string | null
  /** 场地申报状态 */
  cdsbzt: number
  /** 场地申报使用情况 */
  cdsbsyqk: string
  /** 审批状态 */
  spzt: number
  /** 网络教学关联ID */
  wljxglid: number
  /** 学生分组ID */
  xsfzid: number
  /** 学生分组名称 */
  xsfzmc: string
  /** 附件类别 */
  fjlb: string
  /** 考勤照片 */
  kqzp: string
  /** 调停补课申请状态 */
  ttbksqzt: number
  /** 场所确认审核状态 */
  csqrshzt: number
  /** 作业附件类别 */
  zyfjlb: string | null
  /** 直播平台代码 */
  zbptdm: string | null
  /** 直播平台名称 */
  zbptmc: string | null
  /** 直播平台内容 */
  zbptnr: string | null
  /** 教学平台代码 */
  jxptdm: string | null
  /** 教学平台名称 */
  jxptmc: string | null
  /** 教学平台内容 */
  jxptnr: string | null
  /** 学生上课审核状态 */
  xsskshzt: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 主讲教师编号 */
  zdjs: string
  /** 主讲教师姓名 */
  zdjsxm: string
  /** 所属环境 */
  sshb: string | null
}

/**
 * 教师课程表响应
 */
export interface TeacherScheduleResponse {
  /** 课程表列表 */
  items: TeacherScheduleItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总条数 */
  total: number
}

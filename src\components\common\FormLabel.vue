<template>
  <view class="form-label text-sm text-gray-600 mb-[12rpx]">
    {{ text }}
    <text v-if="required" class="text-red-500">*</text>
  </view>
</template>

<script lang="ts" setup>
// Props定义
interface Props {
  /** 标签文本 */
  text: string
  /** 是否必填 */
  required?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  required: false,
})
</script>

<style lang="scss" scoped>
.form-label {
  // 统一的表单标签样式
}
</style>

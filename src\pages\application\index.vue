<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '应用',
  },
}
</route>
<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useMenuStore } from '@/store/menu'
import { useRecentMenuStore } from '@/store/recentMenu'
import type { MenuItem } from '@/store/menu'
import { useUserStore } from '@/store/user'
import { getUserMenus } from '@/service/auth'

// 白名单路径，这些页面不需要登录就能访问
const userStore = useUserStore()

// 页面标题
const pageTitle = ref('应用中心')

// 搜索关键词
const searchKeyword = ref('')

// 当前选中的分类
const activeCategory = ref('全部')

// 分类标签列表
const categories = ref(['全部', '常用', '教学', '办公', '行政', '学生', '科研', '后勤'])

// 获取菜单数据
const menuStore = useMenuStore()
// 获取最近访问菜单数据
const recentMenuStore = useRecentMenuStore()

// 颜色映射
const colorMap: Record<string, string> = {
  blue: 'linear-gradient(135deg, #0a84ff, #0055d4)',
  green: 'linear-gradient(135deg, #34c759, #248a3d)',
  orange: 'linear-gradient(135deg, #ff9500, #c93400)',
  red: 'linear-gradient(135deg, #ff3b30, #c90c00)',
  purple: 'linear-gradient(135deg, #5e5ce6, #3634a3)',
  teal: 'linear-gradient(135deg, #5ac8fa, #0071a4)',
  yellow: 'linear-gradient(135deg, #ffcc00, #d68e00)',
  pink: 'linear-gradient(135deg, #ff2d55, #b3002d)',
}

// 处理路径的工具函数 - 用于规范化和映射路径
const processMenuPath = (menu: MenuItem): MenuItem => {
  if (!menu.path) return menu

  // 处理路径末尾的斜杠 - 去掉URL结尾的斜杠
  let processedPath = menu.path
  if (processedPath.endsWith('/')) {
    processedPath = processedPath.slice(0, -1)
  }

  // 特殊路径处理 - 教师相关路径映射
  const pathMapping: Record<string, string> = {
    '/student/select-course/selected': '/student/select-course',
    '/teacher/my/salary': '/Salary/salaryInfo',
    '/teacher/my/profile': '/Information/teacherInfo',
    '/teacher/address-book': '/Information/teacherContacts',
    '/teacher/work/not-teach': '/teacher/teacher-profile/non-workload',
    '/teacher/teaching-plan/inquiry': '/teachingSchedule/classTable',
    '/teacher/teaching-affairs/attend-lecture': '/teacher/professional-development/attend-lecture',
    '/student/study-plan': '/student/study-plan/index',
    '/student/total-score': '/student/total-score/index',
    '/student/daily-score': '/student/score',
    '/student/change-apply': '/student/schoolChange/schoolChangeList',
  }

  // 检查是否需要路径映射
  if (pathMapping[processedPath]) {
    processedPath = pathMapping[processedPath]
  }

  // 创建菜单对象的副本，更新处理后的路径
  return { ...menu, path: processedPath }
}

// 获取菜单颜色 - 基于菜单标题分配固定颜色
const getMenuColor = (menu: MenuItem): string => {
  const title = menu.meta.title
  const colorAssignment: Record<string, string> = {
    // 顶级菜单颜色
    教师档案: 'blue',
    教学事务: 'green',
    专业发展: 'purple',
    行政服务: 'orange',
    公共服务: 'teal',

    // 学生模块顶级菜单颜色
    入学报到: 'blue',
    学籍管理: 'green',
    课业学习: 'purple',
    考评管理: 'orange',
    实践发展: 'teal',

    // 子菜单颜色
    我的工资: 'orange',
    个人信息: 'blue',
    教工通讯录: 'teal',
    非教学工作量: 'green',
    教师课程表: 'green',
    我的调停课: 'yellow',
    教学日志信息: 'purple',
    我的教学任务: 'blue',
    教师选课申报: 'teal',
    教学工作量: 'red',
    校内邮件信息: 'orange',
    学校通知公告: 'pink',
    出入信息: 'green',
    我的选用教材: 'blue',

    // 学生模块子菜单颜色
    我的个人信息: 'blue',
    我的班级同学: 'green',
    我的课表: 'green',
    我的选课列表: 'teal',
    日常成绩查询: 'yellow',
    我的总评成绩: 'purple',
    我的考勤: 'red',
    我的请假: 'orange',
    校内邮箱: 'teal',
    通知公告: 'pink',
    我的证书信息: 'purple',
    我的家庭成员: 'pink',
    我的学籍异动: 'teal',
    我的领用教材: 'orange',
    我的学习计划: 'blue',
    我的德育分: 'green',

    // 新增教师模块子菜单颜色
    我的培训会议: 'purple',
    教师听课信息: 'blue',
  }

  // 如果没有特定指定，基于菜单ID创建一个固定分配
  if (!colorAssignment[title]) {
    const colors = Object.keys(colorMap)
    const index = menu.id % colors.length
    return colors[index]
  }

  return colorAssignment[title]
}

// 获取顶级菜单(不包含隐藏的菜单)，并根据搜索关键词过滤，同时预处理所有路径
const topMenus = computed(() => {
  const allMenus = menuStore
    .getAllMenus()
    .filter((menu) => menu.type === 'menu_dir' && !menu.meta.hideInMenu)
    .map((menu) => {
      // 创建菜单的副本
      const menuCopy = { ...menu }

      // 预处理子菜单路径
      menuCopy.children = menu.children.map((subMenu) => processMenuPath(subMenu))

      return menuCopy
    })

  // 如果没有搜索关键词，返回所有菜单
  if (!searchKeyword.value.trim()) {
    return allMenus
  }

  // 根据搜索关键词过滤菜单及其子菜单
  return allMenus
    .map((menu) => {
      // 创建菜单的副本，避免修改原始数据
      const menuCopy = { ...menu }

      // 过滤子菜单
      menuCopy.children = menu.children.filter((subMenu) => {
        return subMenu.meta.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
      })

      return menuCopy
    })
    .filter((menu) => menu.children.length > 0) // 仅保留有符合条件子菜单的分类
})

// 获取图标名称 - 返回完整的图标类名
const getIconName = (menu: MenuItem): string => {
  // 根据菜单名称或标题匹配Carbon图标
  const title = menu.meta.title
  const iconMap: Record<string, string> = {
    // 顶级菜单图标
    教师档案: 'user-profile',
    教学事务: 'book',
    专业发展: 'growth',
    行政服务: 'document',
    公共服务: 'help-desk',

    // 学生模块顶级菜单图标
    入学报到: 'login',
    学籍管理: 'user',
    课业学习: 'notebook',
    考评管理: 'chart-bar',
    实践发展: 'growth',

    // 子菜单图标
    我的工资: 'money',
    个人信息: 'user-avatar',
    教工通讯录: 'phone',
    非教学工作量: 'document-tasks',
    教师课程表: 'calendar',
    我的调停课: 'edit',
    教学日志信息: 'notebook',
    我的教学任务: 'task',
    教师选课申报: 'data-table',
    教学工作量: 'chart-bar',
    校内邮件信息: 'email',
    学校通知公告: 'notification',
    出入信息: 'login',
    我的选用教材: 'mdi-book-plus-multiple',

    // 学生模块子菜单图标
    我的个人信息: 'user-avatar',
    我的班级同学: 'group-presentation',
    我的课表: 'calendar',
    我的选课列表: 'data-table',
    日常成绩查询: 'report',
    我的总评成绩: 'chart-bar',
    我的考勤: 'time',
    我的请假: 'edit',
    校内邮箱: 'email',
    通知公告: 'notification',
    我的证书信息: 'certificate',
    我的家庭成员: 'user-multiple',
    我的学籍异动: 'data-share',
    我的领用教材: 'book',
    我的学习计划: 'task-add',
    我的德育分: 'chart-line',

    // 新增教师模块子菜单图标
    我的培训会议: 'event',
    教师听课信息: 'headset',
  }

  const iconName = iconMap[title] || 'application'
  return iconName.indexOf('mdi-') !== -1 ? `i-${iconName}` : `i-carbon-${iconName}`
}

/**
 * 初始化菜单数据
 * 获取最新的菜单数据并存入store
 */
const initMenuData = async () => {
  try {
    // 只有在用户已登录的情况下才获取菜单
    if (userStore.tokenInfo.token) {
      const menus = await getUserMenus()
      menuStore.setMenus(menus)
    }
  } catch (error) {
    console.error('获取菜单数据失败:', error)
  }
}
// 检查菜单数据
onMounted(() => {
  console.log('应用中心页面加载时的菜单数据:', menuStore.menus)
  /* if (menuStore.menus.length === 0) {
    initMenuData()
  } */
  uni.setNavigationBarTitle({
    title: pageTitle.value,
  })
})
onShow(() => {
  if (menuStore.menus.length === 0) {
    initMenuData()
  }
})

// 切换分类
const changeCategory = (category: string) => {
  activeCategory.value = category
}

// 菜单导航 - 使用已经预处理好的路径
const navigateToMenu = (menu: MenuItem) => {
  if (!menu.path) return

  // 记录菜单访问
  const iconClass = getIconName(menu)
  const iconName = iconClass.replace('i-carbon-', '') // 提取图标名称
  const colorName = getMenuColor(menu)
  recentMenuStore.recordMenuVisit(menu, iconName, colorName)

  // 如果是外部链接
  if (menu.path.startsWith('http')) {
    // 处理外部链接
    uni.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(menu.path)}`,
    })
    return
  }

  // TabBar页面列表 - 与pages.config.ts中的tabBar配置保持一致
  const tabBarPages = [
    '/index/index',
    '/application/index',
    '/workflow/index',
    '/Mail/mailList',
    '/about/about',
  ]

  // 检查是否是TabBar页面
  if (tabBarPages.includes(menu.path)) {
    // 使用switchTab跳转到TabBar页面
    uni.switchTab({
      url: `/pages${menu.path}`,
      fail: (err) => {
        console.error('TabBar页面跳转失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
        })
      },
    })
  } else {
    // 内部非TabBar页面导航使用navigateTo
    uni.navigateTo({
      url: `/pages${menu.path}`,
      fail: (err) => {
        console.error('导航失败:', err)
        uni.showToast({
          title: '页面不存在',
          icon: 'none',
        })
      },
    })
  }
}

// 搜索处理函数部分重写
const handleSearch = (event: any) => {
  // 兼容wot-design-uni的wd-input组件和原生input
  if (typeof event === 'string') {
    // 直接传入的字符串值（wd-input的change事件）
    searchKeyword.value = event
  } else if (event?.detail?.value !== undefined) {
    // 小程序原生input事件
    searchKeyword.value = event.detail.value
  } else if (event?.target?.value !== undefined) {
    // H5原生input事件
    searchKeyword.value = event.target.value
  }
}

// 添加搜索框清空功能
const clearSearch = () => {
  searchKeyword.value = ''
}

// 是否有搜索结果
const hasSearchResults = computed(() => {
  return topMenus.value.length > 0
})
</script>

<template>
  <view class="app-container">
    <!-- 标题和搜索框 -->
    <view class="header">
      <view class="page-title">{{ pageTitle }}</view>
      <view class="search-box">
        <wd-input
          v-model="searchKeyword"
          placeholder="搜索应用"
          clearable
          prefix-icon="search"
          clear-trigger="focus"
          @change="handleSearch"
          @input="handleSearch"
          @clear="clearSearch"
        />
      </view>

      <!-- 分类标签 -->
      <!--  <scroll-view scroll-x class="category-tabs">
        <view
          v-for="(category, index) in categories"
          :key="index"
          class="category-tab"
          :class="{ active: activeCategory === category }"
          @tap="changeCategory(category)"
        >
          {{ category }}
        </view>
      </scroll-view> -->
    </view>

    <!-- 搜索结果为空的提示 -->
    <view v-if="!hasSearchResults && searchKeyword" class="empty-search-result">
      <view class="empty-icon">
        <wd-icon name="search" />
      </view>
      <view class="empty-text">未找到"{{ searchKeyword }}"相关应用</view>
    </view>

    <!-- 应用分类区域 -->
    <view v-for="menu in topMenus" :key="menu.id" class="app-section">
      <view class="section-title">
        <view class="section-icon">
          <!-- 使用动态绑定类名 -->
          <view :class="[getIconName(menu), 'text-blue-500']"></view>
        </view>
        {{ menu.meta.title }}
      </view>
      <view class="app-grid">
        <view
          v-for="subMenu in menu.children"
          :key="subMenu.id"
          class="app-item"
          @tap="navigateToMenu(subMenu)"
        >
          <view class="app-icon" :style="{ background: colorMap[getMenuColor(subMenu)] }">
            <!-- 使用动态绑定类名 -->
            <view :class="[getIconName(subMenu), 'text-white', 'icon-size']"></view>
          </view>
          <view class="app-name">{{ subMenu.meta.title }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.app-container {
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  padding: 30rpx;
  padding-bottom: 140rpx;
  background-color: #f2f5f8;
}

.header {
  width: 100%;
  margin-bottom: 30rpx;
}

.page-title {
  margin-bottom: 30rpx;
  font-size: 44rpx;
  font-weight: 700;
}

.search-box {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 30rpx;
}

// 为wot-design-uni组件添加自定义样式
:deep(.wd-input__inner) {
  height: 90rpx !important;
  padding: 0 0 0 10rpx !important;
  font-size: 32rpx !important;
  background-color: #ffffff !important;
  border-radius: 45rpx !important;
  transition: all 0.3s ease !important;
}

:deep(.wd-input__inner:focus) {
  // border-color: rgba(0, 122, 255, 0.5) !important;
  // box-shadow: 0 6rpx 25rpx rgba(0, 122, 255, 0.15) !important;
}

:deep(.wd-input__prefix) {
  left: 00rpx !important;
  margin-left: 30rpx;
}

:deep(.wd-icon) {
  font-size: 40rpx !important;
  color: #007aff !important;
}

:deep(.wd-input__clear) {
  width: 40rpx !important;
  height: 40rpx !important;
  margin-right: 40rpx !important;
  line-height: 40rpx !important;
  color: #999 !important;
  background-color: #f0f0f0 !important;
  border-radius: 50% !important;
  transition: all 0.2s ease !important;
}

:deep(.wd-input__clear:active) {
  background-color: #e0e0e0 !important;
  transform: scale(0.95) !important;
}

.category-tabs {
  display: flex;
  width: 100%;
  margin-bottom: 30rpx;
  white-space: nowrap;
}

.category-tab {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f2f2f7;
  border-radius: 36rpx;
  transition: all 0.3s ease;
}

.category-tab.active {
  color: #fff;
  background-color: #007aff;
  box-shadow: 0 4rpx 10rpx rgba(0, 122, 255, 0.3);
}

.app-section {
  box-sizing: border-box;
  width: 100%;
  padding: 30rpx;
  margin-bottom: 40rpx;
  background-color: #fff;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50rpx;
  height: 50rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
  background-color: #f2f7ff;
  border-radius: 12rpx;
}

.app-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
  width: 100%;
}

.app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.3s ease;
}

.app-item:active {
  transform: scale(0.95);
}

.app-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 110rpx;
  height: 110rpx;
  margin-bottom: 16rpx;
  color: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.icon-size {
  font-size: 42rpx;
}

.app-name {
  font-size: 26rpx;
  color: #333;
}

@media screen and (max-width: 768rpx) {
  .app-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.text-blue-500 {
  color: #007aff;
}

.text-white {
  color: #ffffff;
}

.text-gray-500 {
  color: #999999;
}

.app-section:last-child {
  margin-bottom: 20rpx;
}

.empty-search-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 100rpx 0;
  background-color: #fff;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.05);
}

.empty-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  color: #999;
  background-color: #f5f5f5;
  border-radius: 50%;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
}
</style>

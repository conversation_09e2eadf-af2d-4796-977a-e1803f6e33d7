/**
 * 定位相关工具函数
 */

// 天地图API配置
const TIANDITU_API_KEY = 'fffe1231231223213126892d5b511e9c60'
const TIANDITU_BASE_URL = 'https://api.tianditu.gov.cn'

/**
 * 位置信息接口
 */
export interface LocationInfo {
  /** 经度 */
  longitude: number
  /** 纬度 */
  latitude: number
  /** 详细地址 */
  address: string
  /** 省份 */
  province?: string
  /** 城市 */
  city?: string
  /** 区县 */
  district?: string
  /** 街道 */
  street?: string
  /** 获取时间 */
  timestamp: number
}

/**
 * 天地图逆地理编码响应接口
 */
interface TiandituGeocodeResponse {
  status: string
  result: {
    formatted_address: string
    addressComponent: {
      nation: string
      province: string
      city: string
      county: string
      road: string
      poi: string
      poi_distance: string
      address: string
    }
  }
}

/**
 * 获取当前位置信息
 * @param options 定位选项
 * @returns Promise<LocationInfo>
 */
export function getCurrentLocation(options?: {
  /** 是否高精度定位 */
  enableHighAccuracy?: boolean
  /** 超时时间(ms) */
  timeout?: number
  /** 最大缓存时间(ms) */
  maximumAge?: number
}): Promise<LocationInfo> {
  return new Promise((resolve, reject) => {
    const defaultOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000,
      ...options,
    }

    uni.getLocation({
      type: 'gcj02', // 使用国测局坐标系
      ...defaultOptions,
      success: async (res) => {
        try {
          console.log('获取位置成功:', res)

          // 调用天地图逆地理编码获取详细地址
          const address = await reverseGeocode(res.longitude, res.latitude)

          const locationInfo: LocationInfo = {
            longitude: res.longitude,
            latitude: res.latitude,
            address: address.formatted_address,
            province: address.addressComponent.province,
            city: address.addressComponent.city,
            district: address.addressComponent.county,
            street: address.addressComponent.road,
            timestamp: Date.now(),
          }

          resolve(locationInfo)
        } catch (error) {
          console.error('获取地址信息失败:', error)
          // 即使获取地址失败，也返回基本的经纬度信息
          resolve({
            longitude: res.longitude,
            latitude: res.latitude,
            address: `${res.latitude.toFixed(6)}, ${res.longitude.toFixed(6)}`,
            timestamp: Date.now(),
          })
        }
      },
      fail: (error) => {
        console.error('获取位置失败:', error)
        reject(new Error(`定位失败: ${error.errMsg || '未知错误'}`))
      },
    })
  })
}

/**
 * 天地图逆地理编码 - 根据经纬度获取地址信息
 * @param longitude 经度
 * @param latitude 纬度
 * @returns Promise<TiandituGeocodeResponse['result']>
 */
export function reverseGeocode(
  longitude: number,
  latitude: number,
): Promise<TiandituGeocodeResponse['result']> {
  return new Promise((resolve, reject) => {
    const url = `${TIANDITU_BASE_URL}/geocoder`

    uni.request({
      url,
      method: 'GET',
      data: {
        postStr: JSON.stringify({
          lon: longitude,
          lat: latitude,
          ver: 1,
        }),
        type: 'geocode',
        tk: TIANDITU_API_KEY,
      },
      success: (res) => {
        console.log('天地图逆地理编码响应:', res)

        if (res.statusCode === 200) {
          const data = res.data as TiandituGeocodeResponse
          if (data.status === '0' && data.result) {
            resolve(data.result)
          } else {
            reject(new Error(`天地图API错误: ${data.status}`))
          }
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`))
        }
      },
      fail: (error) => {
        console.error('天地图逆地理编码请求失败:', error)
        reject(new Error(`网络请求失败: ${error.errMsg || '未知错误'}`))
      },
    })
  })
}

/**
 * 格式化位置信息为显示文本
 * @param location 位置信息
 * @returns 格式化后的地址文本
 */
export function formatLocationText(location: LocationInfo): string {
  if (location.address) {
    return location.address
  }

  // 如果没有详细地址，使用省市区拼接
  const parts = [location.province, location.city, location.district, location.street].filter(
    Boolean,
  )

  if (parts.length > 0) {
    return parts.join('')
  }

  // 最后使用经纬度
  return `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`
}

/**
 * 检查是否支持定位功能
 * @returns boolean
 */
export function isLocationSupported(): boolean {
  return typeof uni.getLocation === 'function'
}

/**
 * 计算两点之间的距离（米）
 * @param lat1 第一个点的纬度
 * @param lon1 第一个点的经度
 * @param lat2 第二个点的纬度
 * @param lon2 第二个点的经度
 * @returns 距离（米）
 */
export function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371000 // 地球半径（米）
  const dLat = ((lat2 - lat1) * Math.PI) / 180
  const dLon = ((lon2 - lon1) * Math.PI) / 180
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

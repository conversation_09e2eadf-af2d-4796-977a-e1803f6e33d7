<route lang="json5">
{
  style: {
    navigationBarTitleText: '销假申请',
  },
}
</route>
<template>
  <view class="leave-return-page">
    <FormWithApproval :id="leaveRecord?.id" code="xsqjxjsq" :show-workflow="hasSubmittedReturn">
      <!-- 表单内容插槽 -->
      <template #form-content>
        <view v-if="leaveRecord">
          <!-- 请假信息展示 -->
          <view class="info-card">
            <view class="card-title">请假信息</view>
            <view class="info-content">
              <view class="info-item">
                <text class="info-label">请假类别：</text>
                <text class="info-value">{{ getLeaveCategory() }}</text>
              </view>

              <view class="info-item">
                <text class="info-label">请假人：</text>
                <text class="info-value">{{ leaveRecord.qjrxm || '' }}</text>
              </view>

              <view class="info-item">
                <text class="info-label">请假类型：</text>
                <text class="info-value">{{ leaveRecord.qjlxmc || '' }}</text>
              </view>

              <view class="info-item">
                <text class="info-label">是否出校：</text>
                <text class="info-value">{{ leaveRecord.sfsqcxm ? '是' : '否' }}</text>
              </view>

              <view class="info-item">
                <text class="info-label">请假时间：</text>
                <text class="info-value">{{ formatLeaveTime() }}</text>
              </view>

              <view class="info-item">
                <text class="info-label">请假时长：</text>
                <text class="info-value">{{ leaveRecord.qjss || 0 }}小时</text>
              </view>

              <view class="info-item">
                <text class="info-label">请假天数：</text>
                <text class="info-value">{{ leaveRecord.qjts || 0 }}天</text>
              </view>
            </view>
          </view>

          <!-- 销假表单 -->
          <view class="form-card">
            <view class="card-title">销假信息</view>
            <view class="form-content">
              <!-- 销假说明 -->
              <view class="form-item">
                <view class="form-label">
                  <text>销假说明</text>
                  <text class="text-required">*</text>
                </view>
                <TextareaInput
                  v-model="returnForm.xjsm"
                  placeholder="请输入销假说明..."
                  :maxlength="500"
                  :show-count="true"
                  :auto-height="true"
                  :disabled="isReadOnly"
                />
              </view>

              <!-- 当前定位 -->
              <view class="form-item">
                <view class="form-label">
                  <text>当前定位</text>
                  <text class="text-required">*</text>
                </view>
                <view class="location-container">
                  <view class="location-display">
                    <view v-if="returnForm.currentLocation" class="location-text">
                      <text class="location-icon">📍</text>
                      <text class="location-address">{{ returnForm.currentLocation }}</text>
                    </view>
                    <view v-else class="location-placeholder">
                      <text>请获取当前定位</text>
                    </view>
                  </view>
                  <view class="location-actions">
                    <ActionButton
                      type="secondary"
                      :text="locationLoading ? '定位中...' : '获取定位'"
                      :disabled="locationLoading || isReadOnly"
                      @click="getCurrentLocationInfo"
                      size="small"
                    />
                  </view>
                </view>
                <view v-if="locationError" class="location-error">
                  <text>{{ locationError }}</text>
                </view>
              </view>

              <!-- 销假附件 -->
              <view class="form-item">
                <view class="form-label">
                  <text>销假附件</text>
                </view>
                <FileUploader
                  v-model="returnForm.attachments"
                  upload-type="leave-return"
                  title=""
                  :show-title="false"
                  tip-text="支持上传相关证明材料"
                  empty-text="暂无附件"
                  :count="5"
                />
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-state">
          <text class="empty-text">请假信息不存在</text>
        </view>
      </template>

      <!-- 按钮组插槽 -->
      <template #form-buttons>
        <view v-if="leaveRecord && canSubmit" class="bottom-actions">
          <ActionButton type="secondary" text="取消" @click="goBack" />
          <ActionButton type="primary" text="提交销假" @click="handleSubmit" />
        </view>
        <view v-else-if="leaveRecord && isReadOnly" class="bottom-actions">
          <ActionButton type="primary" text="返回" @click="goBack" />
        </view>
        <view v-else-if="!leaveRecord" class="bottom-actions">
          <ActionButton type="primary" text="返回" @click="goBack" />
        </view>
      </template>
    </FormWithApproval>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useLeaveReturnStore } from '@/store/leaveReturn'
import { submitLeaveReturn } from '@/service/leave'
import { getCurrentLocation, formatLocationText, type LocationInfo } from '@/utils/location'
import TextareaInput from '@/components/common/TextareaInput.vue'
import FileUploader from '@/components/common/FileUploader.vue'
import ActionButton from '@/components/common/ActionButton.vue'
import FormWithApproval from '@/components/FormWithApproval/index.vue'
import type { LeaveRecordItem } from '@/types/leave'

// 使用store
const leaveReturnStore = useLeaveReturnStore()
const leaveRecord = ref<LeaveRecordItem | null>(null)

// 销假表单数据
const returnForm = reactive({
  xjsm: '', // 销假说明
  attachments: [] as Array<{ url: string; name: string }>, // 销假附件
  currentLocation: '', // 当前定位地址
  longitude: 0, // 经度
  latitude: 0, // 纬度
})

// 是否已提交过销假申请
const hasSubmittedReturn = computed(() => {
  return !!(leaveRecord.value?.xjsm || leaveRecord.value?.xjsp !== undefined)
})

// 是否为只读模式（销假审批已通过）
const isReadOnly = computed(() => {
  // 只有销假审批通过(xjsp === 1)时才为只读模式
  return leaveRecord.value?.xjsp === 1
})

// 是否可以提交销假（审批状态不是通过的情况下可以提交）
const canSubmit = computed(() => {
  return !isReadOnly.value
})

// 定位相关状态
const locationLoading = ref(false)
const locationError = ref('')

// 获取当前定位
const getCurrentLocationInfo = async () => {
  if (locationLoading.value) return

  locationLoading.value = true
  locationError.value = ''

  try {
    uni.showLoading({
      title: '获取定位中...',
      mask: true,
    })

    const location = await getCurrentLocation({
      enableHighAccuracy: true,
      timeout: 15000,
    })

    returnForm.currentLocation = formatLocationText(location)
    returnForm.longitude = location.longitude
    returnForm.latitude = location.latitude

    uni.hideLoading()
    uni.showToast({
      title: '定位获取成功',
      icon: 'success',
      duration: 1500,
    })

    console.log('定位信息:', location)
  } catch (error) {
    console.error('获取定位失败:', error)
    locationError.value = error instanceof Error ? error.message : '定位失败'

    uni.hideLoading()
    uni.showToast({
      title: '定位获取失败',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    locationLoading.value = false
  }
}

// 获取请假类别显示
const getLeaveCategory = () => {
  // 根据wqsq字段判断请假类别
  if (!leaveRecord.value) return '晚寝请假'

  const wqsqMap: Record<number, string> = {
    1: '学习请假',
    2: '晚寝请假',
    3: '全部请假',
  }

  return wqsqMap[leaveRecord.value.wqsq] || '晚寝请假'
}

// 格式化请假时间显示
const formatLeaveTime = () => {
  if (!leaveRecord.value || !leaveRecord.value.qjkssj || !leaveRecord.value.qjjssj) {
    return '2025-07-10 00:00:00 至 2025-07-24 00:00:00'
  }
  return `${leaveRecord.value.qjkssj} 至 ${leaveRecord.value.qjjssj}`
}

// 处理提交
const handleSubmit = async () => {
  // 如果是只读模式（销假已审批通过），不允许提交
  if (isReadOnly.value) {
    uni.showToast({
      title: '该销假申请已审批通过，无法重复提交',
      icon: 'none',
    })
    return
  }

  // 验证必填字段
  if (!returnForm.xjsm.trim()) {
    uni.showToast({
      title: '请输入销假说明',
      icon: 'none',
    })
    return
  }

  // 验证定位信息
  if (!returnForm.currentLocation || !returnForm.longitude || !returnForm.latitude) {
    uni.showToast({
      title: '请先获取当前定位',
      icon: 'none',
    })
    return
  }

  // 验证请假信息是否存在
  if (!leaveRecord.value || !leaveRecord.value.id) {
    uni.showToast({
      title: '请假信息不完整',
      icon: 'none',
    })
    return
  }

  try {
    uni.showLoading({
      title: '提交中...',
      mask: true,
    })

    // 处理附件列表，转换为字符串格式
    const fjlb2 = returnForm.attachments.map((file) => `${file.url}|${file.name}`).join('|')

    // 调用销假API
    await submitLeaveReturn({
      id: leaveRecord.value.id,
      wqsq: leaveRecord.value.wqsq || 1,
      qjlx: leaveRecord.value.qjlx || '1',
      sfsqcxm: leaveRecord.value.sfsqcxm || 0,
      qjss: leaveRecord.value.qjss || 0,
      xjsm: returnForm.xjsm.trim(),
      fjlb2,
      longitude: returnForm.longitude,
      latitude: returnForm.latitude,
      currentLocation: returnForm.currentLocation,
    })

    uni.hideLoading()
    uni.showToast({
      title: '申请成功,请等待辅导员审批',
      icon: 'success',
    })

    // 延迟返回上一页
    setTimeout(() => {
      goBack()
    }, 1500)
  } catch (error) {
    console.error('销假申请提交失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '销假申请提交失败',
      icon: 'none',
    })
  }
}

// 返回上一页
const goBack = () => {
  // 清除store中的数据
  leaveReturnStore.clearCurrentLeaveRecord()
  uni.navigateBack()
}

// 回填销假数据
const fillReturnData = () => {
  if (!leaveRecord.value) return

  // 如果已有销假说明，回填数据
  if (leaveRecord.value.xjsm) {
    returnForm.xjsm = leaveRecord.value.xjsm
  }

  // 如果已有销假附件，回填数据
  if (leaveRecord.value.fjlb2) {
    const attachments = leaveRecord.value.fjlb2.split(',').filter((item) => item.trim())
    returnForm.attachments = attachments.map((item) => {
      const [url, name] = item.split('|')
      return {
        url: url || item,
        name: name || '附件',
      }
    })
  }
}

// 页面加载时获取数据
onMounted(() => {
  leaveRecord.value = leaveReturnStore.currentLeaveRecord

  if (!leaveRecord.value) {
    uni.showToast({
      title: '请假信息不存在',
      icon: 'none',
    })
    setTimeout(() => {
      goBack()
    }, 1500)
    return
  }

  // 回填销假数据
  fillReturnData()

  // 自动获取当前定位（仅在非只读模式下）
  if (!isReadOnly.value) {
    getCurrentLocationInfo()
  }
})
</script>

<style lang="scss" scoped>
.leave-return-page {
  min-height: 100vh;
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
}

.page-header {
  padding: 32rpx 24rpx;
  background-color: #fff;
  border-bottom: 2rpx solid #eee;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.page-content {
  padding: 24rpx;
}

.info-card,
.form-card {
  margin-bottom: 24rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 16rpx;
}

.card-title {
  padding: 32rpx 24rpx 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 2rpx solid #f0f0f0;
}

.info-content,
.form-content {
  padding: 24rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  min-width: 160rpx;
  font-weight: 500;
  color: #666;
}

.info-value {
  flex: 1;
  color: #333;
  word-break: break-all;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.text-required {
  margin-left: 4rpx;
  color: #ff4d4f;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 24rpx;
}

.empty-text {
  margin-bottom: 32rpx;
  font-size: 28rpx;
  color: #999;
}

.bottom-actions {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  gap: 24rpx;
  padding: 24rpx;
  background-color: #fff;
  border-top: 2rpx solid #eee;
}

.bottom-actions .action-button {
  flex: 1;
}
/* 定位相关样式 */
.location-container {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.location-display {
  display: flex;
  flex: 1;
  align-items: center;
  min-height: 80rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
}

.location-text {
  display: flex;
  gap: 8rpx;
  align-items: center;
  width: 100%;
}

.location-icon {
  font-size: 28rpx;
  color: #007bff;
}

.location-address {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.4;
  color: #333;
  word-break: break-all;
}

.location-placeholder {
  font-size: 28rpx;
  color: #999;
}

.location-actions {
  flex-shrink: 0;
}

.location-error {
  padding: 8rpx 16rpx;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #e53e3e;
  background-color: #fff5f5;
  border: 2rpx solid #fed7d7;
  border-radius: 8rpx;
}
</style>

import request from '@/utils/request'
import type {
  TeacherInfo,
  NonWorkloadQuery,
  NonWorkloadResponse,
  TeacherContactQuery,
  TeacherContactResponse,
  SelectCourseInfo,
  SelectCourseInfoParams,
  TeacherWorkloadQuery,
  TeacherWorkloadResponse,
  TeachingLogQuery,
  TeachingLogResponse,
  TeacherWorkloadStatisticsResponse,
  AttendLectureQuery,
  AttendLectureResponse,
  EvaluationProjectQuery,
  EvaluationProjectResponse,
  TeacherScheduleQuery,
  TeacherScheduleResponse,
  TeacherAttendListQuery,
  TeacherAttendListResponse,
  TeacherScheduleInfo,
  UpdateTeacherAttendRequest,
  UpdateTeacherAttendResponse,
  UpdateTeacherScheduleRequest,
  TeacherScheduleStatistics,
  ScoreTypeInfoQuery,
  ScoreTypeInfoResponse,
  TeachingTaskTotalScoreSyncRequest,
  TeachingTaskTotalScoreSyncResponse,
  TeachingTaskTotalScoreUpdateRequest,
  TeachingTaskTotalScoreUpdateResponse,
  DailyScoreMarkListQuery,
  DailyScoreMarkListResponse,
  DailyScoreMarkEditQuery,
  DailyScoreMarkDetail,
  UpdateDailyScoreMarkRequest,
  UpdateDailyScoreMarkResponse,
  AddDailyScoreMarkRequest,
  DeleteDailyScoreMarkRequest,
  DailyScoreTypeInfoQuery,
  DailyScoreTypeInfoResponse,
  DailyScoreStatusEditRequest,
  DailyScoreTypeSetRequest,
  DailyScoreUpdateRequest,
  DailyScoreUpdateItem,
  TotalScoreRevokeDetailQuery,
  TotalScoreRevokeDetailResponse,
  TotalScoreRevokeApplyRequest,
  TotalScoreRevokeApplyResponse,
  TeachingPointDetail,
  UpdateTeachingPointRequest,
  UpdateTeachingPointResponse,
  TeacherScheduleApplyConfirmResponse,
  SubmitTeacherScheduleApplyConfirmRequest,
  SubmitTeacherScheduleApplyConfirmResponse,
  SaveTeachingProcessRequest,
  SaveTeachingProcessResponse,
  SaveTeachingPlanRequest,
  SaveTeachingPlanResponse,
  TeachingCheckQuery,
  TeachingCheckResponse,
  TeachingCheckData,
  TaskAttendanceQuery,
  TaskAttendanceResponse,
  GetArrangeSiteListRequest,
  GetArrangeSiteListResponse,
  ArrangeTeachingSiteRequest,
  ArrangeTeachingSiteResponse,
} from '@/types/teacher'
import type {
  TeachingMaterialChangeQuery,
  TeachingMaterialChangeResponse,
} from '@/types/teachingMaterial'

// 获取教师个人信息
export function getTeacherInfo(): Promise<TeacherInfo> {
  return request('/teacher/teacherInfo', {
    method: 'POST',
    data: {},
  })
}

// 保存教师个人信息
export function saveTeacherInfo(data: Partial<TeacherInfo>): Promise<any> {
  return request('/teacher/saveTeacherInfo', {
    method: 'POST',
    data,
  })
}

// 获取非教学工作量列表
export function getNonWorkloadList(params: NonWorkloadQuery): Promise<NonWorkloadResponse> {
  return request('/teacher/nonWorkload', {
    method: 'POST',
    data: params,
  })
}

// 获取教工通讯录列表
export function getTeacherContacts(params: TeacherContactQuery): Promise<TeacherContactResponse> {
  return request('/teacher/teacherContacts', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教师选课信息
 * @param params 参数
 * @returns 选课信息
 */
export function getSelectCourseInfo(params: SelectCourseInfoParams): Promise<SelectCourseInfo> {
  return request('/teacher/selectCourse/info', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教师工作量列表
 * @param params 查询参数
 * @returns 工作量列表
 */
export function getTeacherWorkload(params: TeacherWorkloadQuery): Promise<TeacherWorkloadResponse> {
  return request('/teacher/workload', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教学日志列表
 * @param params 查询参数
 * @returns 教学日志列表
 */
export function getTeachingLogList(params: TeachingLogQuery): Promise<TeachingLogResponse> {
  return request('/teacher/teachingLog', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教师工作量统计
 * @returns 教师工作量统计数据
 */
export function getTeacherWorkloadStatistics(): Promise<TeacherWorkloadStatisticsResponse> {
  return request('/teacher/workloadStatistics', {
    method: 'POST',
    data: {},
  })
}

/**
 * 获取听课记录列表
 * @param params 查询参数
 * @returns 听课记录列表
 */
export function getAttendLectureList(params: AttendLectureQuery): Promise<AttendLectureResponse> {
  return request('/teacher/attendLecture', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教材变更列表
 * @param params 查询参数
 * @returns 教材变更列表响应数据
 */
export function getTeachingTaskMaterialChangeList(
  params: TeachingMaterialChangeQuery,
): Promise<TeachingMaterialChangeResponse> {
  return request('/teacher/teachingTask/teachingMaterial/changeList', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取听课评估项目列表
 * @param params 查询参数
 * @returns 听课评估项目列表
 */
export function getEvaluationProject(
  params: EvaluationProjectQuery,
): Promise<EvaluationProjectResponse> {
  return request('/teacher/attendLecture/evaluationProject', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教师课程表列表
 * @param params 查询参数
 * @returns 教师课程表列表
 */
export function getTeacherScheduleList(
  params: TeacherScheduleQuery,
): Promise<TeacherScheduleResponse> {
  return request('/teacher/schedule/list', {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
  })
}

/**
 * 获取教师考勤学生列表
 * @param params 查询参数
 * @returns 考勤学生列表响应
 */
export function getTeacherAttendList(
  params: TeacherAttendListQuery,
): Promise<TeacherAttendListResponse> {
  return request('/teacher/schedule/attendList', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教师课程安排详情
 * @param params 课程安排ID参数
 * @returns 课程安排详情信息
 */
export function getTeacherScheduleInfo(id: number): Promise<TeacherScheduleInfo> {
  return request(`/teacher/schedule/info/${id}`, {
    method: 'POST',
    data: {},
  })
}
export function getStudentScheduleInfo(id: number): Promise<TeacherScheduleInfo> {
  return request(`/student/schedule/info/${id}`, {
    method: 'POST',
    data: {},
  })
}
export function getStudentAttendList(
  params: TeacherAttendListQuery,
): Promise<TeacherAttendListResponse> {
  return request('/student/schedule/attendList', {
    method: 'POST',
    data: params,
  })
}

/**
 * 更新教师考勤记录
 * @param params 更新考勤参数
 * @returns 更新结果
 */
export function updateStudentAttend(
  params: UpdateTeacherAttendRequest,
): Promise<UpdateTeacherAttendResponse> {
  return request('/student/schedule/updateAttend', {
    method: 'POST',
    data: params,
  })
}
/**
 * 更新教师考勤记录
 * @param params 更新考勤参数
 * @returns 更新结果
 */
export function updateTeacherAttend(
  params: UpdateTeacherAttendRequest,
): Promise<UpdateTeacherAttendResponse> {
  return request('/teacher/schedule/updateAttend', {
    method: 'POST',
    data: params,
  })
}

/**
 * 更新教师课程安排
 * @param params 更新课程安排参数
 * @returns 更新结果
 */
export function updateTeacherSchedule(params: UpdateTeacherScheduleRequest): Promise<any> {
  return request('/teacher/schedule/update', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教师课程表统计数据
 * @returns 教师课程表统计数据
 */
export function getTeacherScheduleStatistics(): Promise<TeacherScheduleStatistics> {
  return request('/teacher/schedule/stat', {
    method: 'GET',
    data: {},
  })
}

/**
 * 获取教学任务总分类型信息
 * @param params 查询参数
 * @returns 总分类型信息
 */
export function getScoreTypeInfo(params: ScoreTypeInfoQuery): Promise<ScoreTypeInfoResponse> {
  return request('/teacher/teachingTask/totalScore/scoreTypeInfo', {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
  })
}

/**
 * 同步教学任务总分
 * @param params 同步参数
 * @returns 同步结果
 */
export function syncTeachingTaskTotalScore(
  params: TeachingTaskTotalScoreSyncRequest,
): Promise<TeachingTaskTotalScoreSyncResponse> {
  return request('/teacher/teachingTask/totalScore/sync', {
    method: 'POST',
    data: params,
  })
}

/**
 * 更新教学任务总分
 * @param params 更新参数
 * @returns 更新结果
 */
export function updateTeachingTaskTotalScore(
  params: TeachingTaskTotalScoreUpdateRequest,
): Promise<TeachingTaskTotalScoreUpdateResponse['data']> {
  return request('/teacher/teachingTask/totalScore/update', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教师日常评分标记列表
 * @param params 查询参数
 * @returns 日常评分标记列表响应
 */
export function getDailyScoreMarkList(
  params: DailyScoreMarkListQuery,
): Promise<DailyScoreMarkListResponse> {
  return request('/teacher/teachingTask/dailyScoreMark/list', {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
  })
}

/**
 * 获取日常成绩标记编辑详情
 * @param params 查询参数
 * @returns 日常成绩标记详情
 */
export function getDailyScoreMarkEdit(
  params: DailyScoreMarkEditQuery,
): Promise<DailyScoreMarkDetail> {
  return request('/teacher/teachingTask/dailyScoreMark/edit', {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
  })
}

/**
 * 更新日常成绩标记信息
 * @param params 更新参数
 * @returns 更新结果
 */
export function updateDailyScoreMark(
  params: UpdateDailyScoreMarkRequest,
): Promise<UpdateDailyScoreMarkResponse> {
  return request('/teacher/teachingTask/dailyScoreMark/edit', {
    method: 'POST',
    data: params,
  })
}

/**
 * 添加日常成绩标记信息
 * @param params 添加参数
 * @returns 添加结果
 */
export function addDailyScoreMark(params: AddDailyScoreMarkRequest): Promise<null> {
  return request('/teacher/teachingTask/dailyScoreMark/add', {
    method: 'POST',
    data: params,
  })
}

/**
 * 删除日常成绩
 * @param params 删除日常成绩参数
 * @returns 删除结果
 */
export function deleteDailyScoreMark(params: DeleteDailyScoreMarkRequest): Promise<null> {
  return request('/teacher/teachingTask/dailyScoreMark/del', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教学任务日常分数类型信息
 * @param params 查询参数
 * @returns 日常分数类型信息
 */
export function getDailyScoreTypeInfo(
  params: DailyScoreTypeInfoQuery,
): Promise<DailyScoreTypeInfoResponse> {
  return request('/teacher/teachingTask/dailyScore/scoreTypeInfo', {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
  })
}

/**
 * 修改日常成绩状态（提交状态和查询状态）
 * @param params 日常成绩状态修改参数
 * @returns 修改结果
 */
export function editDailyScoreStatus(params: DailyScoreStatusEditRequest): Promise<null> {
  return request('/teacher/teachingTask/dailyScoreMark/edit', {
    method: 'POST',
    data: params,
  })
}

/**
 * 设置日常成绩评定类型
 * @param params 请求参数
 * @returns 设置结果
 */
export function setDailyScoreType(params: DailyScoreTypeSetRequest): Promise<boolean> {
  return request('/teacher/teachingTask/dailyScore/setScoreType', {
    method: 'POST',
    data: params,
  })
}

/**
 * 更新日常成绩
 * @param params 更新参数
 * @returns 更新结果
 */
export function updateDailyScore(params: DailyScoreUpdateRequest): Promise<DailyScoreUpdateItem[]> {
  return request('/teacher/teachingTask/dailyScore/update', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取总评成绩撤销申请详情
 * @param params 请求参数
 * @returns 总评成绩撤销申请详情
 */
export function getTotalScoreRevokeDetail(
  params: TotalScoreRevokeDetailQuery,
): Promise<TotalScoreRevokeDetailResponse> {
  return request('/teacher/teachingTask/totalScore/apply', {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
  })
}

/**
 * 提交总评成绩撤销申请
 * @param params 请求参数
 * @returns 提交结果
 */
export function submitTotalScoreRevokeApply(
  params: TotalScoreRevokeApplyRequest,
): Promise<TotalScoreRevokeApplyResponse> {
  return request('/teacher/teachingTask/totalScore/apply', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取授课要点详情
 * @param id 教学任务ID
 * @returns 授课要点详情
 */
export function getTeachingPointDetail(id: number): Promise<TeachingPointDetail> {
  return request(`/teacher/teachingTask/lessonPoint/${id}`, {
    method: 'GET',
  })
}

/**
 * 更新授课要点
 * @param id 教学任务ID
 * @param data 授课要点数据
 * @returns 更新结果
 */
export function updateTeachingPoint(
  id: number,
  data: UpdateTeachingPointRequest,
): Promise<UpdateTeachingPointResponse> {
  return request(`/teacher/teachingTask/lessonPoint/${id}`, {
    method: 'POST',
    data,
  })
}

/**
 * 获取教师课程安排确认信息
 * @param skjhid 课程安排ID
 * @returns 课程安排确认信息
 */
export function getTeacherScheduleApplyConfirm(
  skjhid: number,
): Promise<TeacherScheduleApplyConfirmResponse> {
  return request(`/teacher/schedule/applyConfirm`, {
    method: 'GET',
    params: { skjhid },
  })
}

/**
 * 提交教师课程安排确认
 * @param params 提交参数
 * @returns 提交结果
 */
export function submitTeacherScheduleApplyConfirm(
  params: SubmitTeacherScheduleApplyConfirmRequest,
): Promise<SubmitTeacherScheduleApplyConfirmResponse> {
  return request('/teacher/schedule/applyConfirm', {
    method: 'POST',
    data: params,
  })
}

/**
 * 保存教学进度
 * @param params 保存教学进度请求参数
 * @returns 保存结果
 */
export function saveTeachingProcess(
  params: SaveTeachingProcessRequest,
): Promise<SaveTeachingProcessResponse['data']> {
  return request('/teacher/teachingTask/TeachingScheduleArrangement/saveTeachingProcess', {
    method: 'POST',
    data: params,
  })
}

/**
 * 提交教学进度
 * @param params 提交教学进度请求参数
 * @returns 提交结果
 */
export function submitTeachingProcess(params: SaveTeachingProcessRequest): Promise<boolean> {
  return request('/teacher/teachingTask/TeachingScheduleArrangement/submitTeachingProcess', {
    method: 'POST',
    data: params,
  })
}

/**
 * 保存授课计划 - 切换场地时调用
 * @param params 保存授课计划请求参数
 * @returns 保存结果
 */
export function saveTeachingPlan(
  params: SaveTeachingPlanRequest,
): Promise<SaveTeachingPlanResponse['data']> {
  return request('/teacher/teachingTask/TeachingScheduleArrangement/savePlan', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教学检查数据
 * @param params 查询参数
 * @param params.opType 操作类型，1-期初检查，2-期中检查，3-期末检查
 * @returns 教学检查数据
 */
export function getTeachingCheck(params: TeachingCheckQuery): Promise<TeachingCheckResponse> {
  return request('/teacher/teachingTask/teachingCheck', {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
  })
}

/**
 * 提交教学检查数据
 * @param jxrwid 教学任务ID
 * @param data 检查数据
 * @returns 提交结果
 */
export function submitTeachingCheck(
  jxrwid: string | number,
  data: Partial<TeachingCheckData>,
): Promise<any> {
  return request('/teacher/teachingTask/submitTeachingCheck', {
    method: 'POST',
    data: {
      jxrwid,
      ...data,
    },
  })
}

/**
 * 提交教学检查保存数据
 * @param jxrwid 教学任务ID
 * @param data 检查数据
 * @param opType 操作类型，1-期初检查，2-期中检查，3-期末检查
 * @param xxxnum 检查项目数量
 * @returns 提交结果
 */
export function teachingCheckSave(
  jxrwid: string | number,
  data: Partial<TeachingCheckData>,
  opType: number,
  xxxnum: number,
): Promise<any> {
  return request('/teacher/teachingTask/teachingCheckSave', {
    method: 'POST',
    data: {
      jxrwid,
      opType,
      xxxnum,
      ...data,
    },
  })
}

/**
 * 获取教师任务考勤列表
 * @param jxrwid 教学任务ID
 * @param params 查询参数
 * @returns 考勤列表响应
 */
export function getTaskAttendance(
  jxrwid: number,
  params: TaskAttendanceQuery,
): Promise<TaskAttendanceResponse> {
  return request(`/teacher/teachingTask/taskAttendance/${jxrwid}`, {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
  })
}

/**
 * 获取授课安排场地列表
 * @param params 请求参数
 * @returns 场地列表响应
 */
export function getArrangeSiteList(
  params: GetArrangeSiteListRequest,
): Promise<GetArrangeSiteListResponse> {
  return request('/teacher/teachingTask/TeachingScheduleArrangement/getArrangeSiteList', {
    method: 'POST',
    data: params,
  })
}

/**
 * 安排授课场地
 * @param params 请求参数
 * @returns 安排结果响应
 */
export function arrangeTeachingSite(
  params: ArrangeTeachingSiteRequest,
): Promise<ArrangeTeachingSiteResponse> {
  return request('/teacher/teachingTask/TeachingScheduleArrangement/arrange', {
    method: 'POST',
    data: params,
  })
}

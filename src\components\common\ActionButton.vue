<template>
  <view
    class="action-button border rounded-md transition-colors cursor-pointer text-center"
    :class="[buttonClass, sizeClass]"
    @click="handleClick"
  >
    <slot>{{ text }}</slot>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

// 按钮类型定义
export type ButtonType =
  | 'primary'
  | 'danger'
  | 'warning'
  | 'secondary'
  | 'default'
  | 'success'
  | 'outline'

// 按钮尺寸定义
export type ButtonSize = 'mini' | 'small' | 'normal' | 'large'

// Props定义
interface Props {
  /** 按钮类型 */
  type?: ButtonType
  /** 按钮尺寸 */
  size?: ButtonSize
  /** 按钮文本 */
  text?: string
  /** 是否禁用 */
  disabled?: boolean
}

// 事件定义
interface Emits {
  (e: 'click'): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  size: 'normal',
  text: '',
  disabled: false,
})

const emit = defineEmits<Emits>()

// 计算按钮样式类
const buttonClass = computed(() => {
  if (props.disabled) {
    return 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
  }

  const typeClasses: Record<ButtonType, string> = {
    primary: 'bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100 active:bg-blue-200',
    danger: 'bg-red-50 text-red-600 border-red-200 hover:bg-red-100 active:bg-red-200',
    warning:
      'bg-orange-50 text-orange-600 border-orange-200 hover:bg-orange-100 active:bg-orange-200',
    secondary: 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100 active:bg-gray-200',
    default: 'bg-gray-50 text-gray-500 border-gray-200 hover:bg-gray-100',
    success: 'bg-green-50 text-green-600 border-green-200 hover:bg-green-100 active:bg-green-200',
    outline: 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50 active:bg-gray-100',
  }

  return typeClasses[props.type]
})

// 计算按钮尺寸样式类
const sizeClass = computed(() => {
  const sizeClasses: Record<ButtonSize, string> = {
    mini: 'px-2 py-1 text-xs',
    small: 'px-2.5 py-1.5 text-sm',
    normal: 'px-3 py-1.5 text-sm',
    large: 'px-4 py-2 text-base',
  }

  return sizeClasses[props.size]
})

// 处理点击事件
const handleClick = (event: Event) => {
  if (!props.disabled) {
    // 阻止事件冒泡
    event.stopPropagation()
    emit('click')
  }
}
</script>

<style lang="scss" scoped>
// 组件样式已通过UnoCSS类实现，无需额外样式
</style>

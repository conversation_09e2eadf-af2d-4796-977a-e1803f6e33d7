import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { LeaveRecordItem } from '@/types/leave'

export const useLeaveReturnStore = defineStore(
  'leaveReturn',
  () => {
    // 当前选中的请假记录
    const currentLeaveRecord = ref<LeaveRecordItem | null>(null)

    // 设置当前请假记录
    const setCurrentLeaveRecord = (record: LeaveRecordItem) => {
      currentLeaveRecord.value = record
    }

    // 清除当前请假记录
    const clearCurrentLeaveRecord = () => {
      currentLeaveRecord.value = null
    }

    // 获取当前请假记录
    const getCurrentLeaveRecord = () => {
      return currentLeaveRecord.value
    }

    return {
      currentLeaveRecord,
      setCurrentLeaveRecord,
      clearCurrentLeaveRecord,
      getCurrentLeaveRecord,
    }
  },
  {
    persist: {
      storage: localStorage,
      paths: ['currentLeaveRecord'],
    },
  },
)

<template>
  <view class="textarea-container">
    <textarea
      :value="modelValue"
      @input="handleInput"
      class="form-textarea w-full p-[16rpx] bg-gray-50 rounded-lg text-sm box-border"
      :placeholder="placeholder"
      :maxlength="maxlength"
      :show-count="showCount"
      :auto-height="autoHeight"
      :disabled="disabled"
    />
  </view>
</template>

<script lang="ts" setup>
// Props定义
interface Props {
  /** 输入值 */
  modelValue?: string
  /** 占位文本 */
  placeholder?: string
  /** 最大长度 */
  maxlength?: number
  /** 是否显示字符计数 */
  showCount?: boolean
  /** 是否自动调整高度 */
  autoHeight?: boolean
  /** 是否禁用 */
  disabled?: boolean
}

// 事件定义
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'input', event: any): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  maxlength: 500,
  showCount: true,
  autoHeight: true,
  disabled: false,
})

const emit = defineEmits<Emits>()

// 处理输入变化
const handleInput = (event: any) => {
  const value = event.detail.value
  emit('update:modelValue', value)
  emit('input', event)
}
</script>

<style lang="scss" scoped>
.textarea-container {
  // 文本域容器样式
}

.form-textarea {
  // 统一的表单文本域样式
  &:disabled {
    color: #999;
    background-color: #f5f5f5;
  }
}
</style>

<template>
  <picker
    :value="selectedIndex"
    :range="options"
    @change="handleChange"
    :disabled="disabled"
    class="form-picker"
  >
    <view
      class="form-input flex items-center justify-between p-[16rpx] rounded-lg"
      :class="inputClass"
    >
      <text class="text-sm" :class="textClass">
        {{ displayText }}
      </text>
      <wd-icon name="arrow-right" custom-style="color: #999; font-size: 28rpx;" />
    </view>
  </picker>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

// Props定义
interface Props {
  /** 当前选中的索引 */
  modelValue?: number
  /** 选项数组 */
  options: string[]
  /** 占位文本 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 当前选中的值（用于显示） */
  selectedValue?: string
  /** 禁用时的占位文本 */
  disabledPlaceholder?: string
}

// 事件定义
interface Emits {
  (e: 'update:modelValue', value: number): void
  (e: 'change', event: any): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: -1,
  placeholder: '请选择',
  disabled: false,
  selectedValue: '',
  disabledPlaceholder: '请先选择上级选项',
})

const emit = defineEmits<Emits>()

// 计算选中的索引
const selectedIndex = computed(() => props.modelValue)

// 计算显示的文本
const displayText = computed(() => {
  if (props.disabled && !props.selectedValue) {
    return props.disabledPlaceholder
  }
  return props.selectedValue || props.placeholder
})

// 计算输入框样式类
const inputClass = computed(() => {
  return props.disabled ? 'bg-gray-200' : 'bg-gray-50'
})

// 计算文本样式类
const textClass = computed(() => {
  return {
    'text-gray-400': !props.selectedValue || props.disabled,
  }
})

// 处理选择变化
const handleChange = (event: any) => {
  const index = event.detail.value
  emit('update:modelValue', index)
  emit('change', event)
}
</script>

<style lang="scss" scoped>
.form-picker {
  // 选择器样式
}

.form-input {
  // 统一的表单输入框样式
}
</style>

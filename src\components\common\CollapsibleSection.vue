<template>
  <view class="collapsible-section">
    <!-- 标题栏 -->
    <view
      class="section-header flex items-center justify-between mb-[12rpx]"
      @click="toggleExpanded"
    >
      <text class="section-title text-sm text-gray-600">{{ title }}</text>
      <view class="toggle-button flex items-center text-xs text-blue-500">
        <text>{{ isExpanded ? '收起' : '展开' }}</text>
        <wd-icon
          :name="isExpanded ? 'arrow-up' : 'arrow-down'"
          custom-style="color: #0083ff; font-size: 24rpx; margin-left: 8rpx;"
        />
      </view>
    </view>

    <!-- 可折叠的内容区域 -->
    <view class="section-content" :class="{ 'section-content-expanded': isExpanded }">
      <slot></slot>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

// Props定义
interface Props {
  /** 标题文本 */
  title: string
  /** 默认是否展开 */
  defaultExpanded?: boolean
  /** 外部控制展开状态 */
  expanded?: boolean
}

// 事件定义
interface Emits {
  (e: 'toggle', expanded: boolean): void
  (e: 'update:expanded', expanded: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  defaultExpanded: false,
  expanded: undefined,
})

const emit = defineEmits<Emits>()

// 内部展开状态
const isExpanded = ref(props.defaultExpanded)

// 监听外部传入的展开状态
watch(
  () => props.expanded,
  (newValue) => {
    if (newValue !== undefined) {
      isExpanded.value = newValue
    }
  },
  { immediate: true },
)

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
  emit('toggle', isExpanded.value)
  emit('update:expanded', isExpanded.value)
}
</script>

<style lang="scss" scoped>
.toggle-button {
  cursor: pointer;
  transition: color 0.2s;

  &:hover {
    color: #0066cc;
  }
}

.section-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;

  &.section-content-expanded {
    max-height: 1000rpx; // 足够大的高度以容纳所有内容
  }
}
</style>

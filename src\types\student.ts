/**
 * 学生同班同学信息接口
 */
export interface ClassmateInfo {
  /** 学号 */
  studentCode: string
  /** 学生姓名 */
  studentName: string
  /** 座位号 */
  seatNum: number | null
  /** 性别: 1-男, 2-女 */
  sex: number
  /** 手机号 */
  phone: string
  /** 班级编号 */
  classCode: string
  /** 班级名称 */
  className: string
  /** 专业编号 */
  majorCode: string
  /** 专业名称 */
  majorName: string
}

/**
 * 班级学生列表响应接口
 */
export interface ClassmatesResponse {
  code: number
  msg: string
  time: number
  data: ClassmateInfo[]
}

/**
 * 班委信息接口
 */
export interface CommitteeMember {
  id: number
  name: string
  avatar: string
  role: string
  borderColor: string
  studentCode?: string
}

/**
 * 字母索引的学生分组
 */
export interface LetterGroupedClassmates {
  letter: string
  students: StudentDisplay[]
}

/**
 * 用于显示的学生信息
 */
export interface StudentDisplay {
  id: string
  name: string
  avatarText: string
  avatarColor: string
  isMe: boolean
  sex: number
  phone: string
  seatNum?: number | null
}

/**
 * 学习小组信息
 */
export interface StudyGroup {
  id: number
  name: string
  count: number
  members: {
    name: string
    avatar: string
    studentCode?: string
  }[]
}

/**
 * 班级基本信息
 */
export interface ClassInfo {
  className: string
  shortName: string
  headTeacher: string
  monitor: string
  studentCount: number
  groupCount: number
}

export interface StudentInfo {
  /** 学生ID */
  id: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: string
  /** 删除标记 */
  deltag: number
  /** 学号 */
  studentCode: string
  /** 学生姓名 */
  studentName: string
  /** 准考证号 */
  ticketCode: string
  /** 座位号 */
  seatNumber: number
  /** 入学日期 */
  startStudyDate: string
  /** 所属学院 */
  ssxy: string
  /** 院系代码 */
  deptCode: string
  /** 院系名称 */
  deptName: string
  /** 专业代码 */
  majorCode: string
  /** 专业名称 */
  majorName: string
  /** 班级代码 */
  classCode: string
  /** 班级名称 */
  className: string
  /** 学生类别 */
  stuCategory: string
  /** 学生类别名称 */
  stuCategoryName: string
  /** 学籍状态 */
  stuStatus: string
  /** 学籍注册状态 */
  stuSchoolStatus: string
  /** 毕业状态 */
  graduateStatus: string
  /** 毕业证书 */
  graduateCert: string
  /** 毕业检查结果 */
  graduationCheckResult: number
  /** 毕业检查描述 */
  graduationCheckDesc: string
  /** 学生形式代码 */
  studentFormCode: string
  /** 学生形式名称 */
  studentFormName: string
  /** 是否优秀毕业生 */
  excellentGraduate: string
  /** 毕业证书编号 */
  graduateCertNum: string
  /** 毕业日期 */
  bysj: string
  /** 银行信息 */
  bankInfo: string
  /** 人员编号 */
  rybh: string
  /** 姓名 */
  name: string
  /** 性别 */
  gender: string
  /** 证件类型代码 */
  IDTypeCode: string
  /** 证件类型名称 */
  IDTypeName: string
  /** 证件号码 */
  IDNumber: string
  /** 户口类别代码 */
  accountsCategoryCode: string
  /** 户口类别名称 */
  accountsCategoryName: string
  /** 户口地区 */
  hkdq: string
  /** 生源地名称 */
  originCountyName: string
  /** 户口地区(省份) */
  hkdqsf: string
  /** 户口地区省份名称 */
  hkdqsfmc: string
  /** 户口地区(城市) */
  hkdqcs: string
  /** 户口地区城市名称 */
  hkdqcsmc: string
  /** 出生日期 */
  birthDate: string
  /** 民族代码 */
  ethnicGroupCode: string
  /** 民族名称 */
  ethnicGroupName: string
  /** 政治面貌代码 */
  politicalOutlookCode: string
  /** 政治面貌名称 */
  politicalOutlookName: string
  /** 婚姻状况代码 */
  marriageCode: string
  /** 婚姻状况名称 */
  marriageName: string
  /** 家庭电话 */
  homePhone: string
  /** 办公电话 */
  officePhone: string | null
  /** 手机号码 */
  mobile: string
  /** QQ号 */
  qqh: string | null
  /** 电子邮箱 */
  email: string
  /** 家庭住址 */
  homeAddress: string
  /** 邮政编码 */
  Zip: string
  /** 学历代码 */
  educationalCode: string | null
  /** 学历名称 */
  educationalName: string | null
  /** 学位代码 */
  degreeCode: string | null
  /** 学位名称 */
  degreeName: string | null
  /** 毕业学校 */
  graduationSchool: string | null
  /** 毕业专业 */
  graduationMajor: string | null
  /** 拼音快速索引 */
  pykssy: string
  /** 用户名 */
  username: string
  /** 昵称 */
  nickname: string
  /** 头像 */
  avatar: string
  /** 密码 */
  password: string
  /** 格言 */
  motto: string
  /** 密码盐值 */
  pwd_salt: string
  /** 密码错误次数 */
  mmcwcs: number
  /** 状态 */
  status: number
  /** 最后登录时间 */
  last_login_time: number
  /** 最后登录IP */
  last_login_ip: string
  /** 登录次数 */
  login_count: number
  /** 父母姓名 */
  parentsName: string
  /** 父母电话 */
  parentsPhone: string
  /** 操作人员 */
  czry: string
  /** 操作员名称 */
  operatorName: string
  /** 住宿方式代码 */
  lodgingCode: string
  /** 住宿方式名称 */
  lodgingName: string
  /** 头像URL */
  avatarUrl: string
  /** 银行 */
  bank: string
  /** 银行卡号 */
  bankCards: string
  /** 生源地区 */
  sydq: string
  /** 生源地区名称 */
  sydqmc: string
  /** 生源地区(省份) */
  sydqsf: string
  /** 生源地区省份名称 */
  sydqsfmc: string
  /** 生源地区(城市) */
  sydqcs: string
  /** 生源地区城市名称 */
  sydqcsmc: string
  /** 宿舍楼代码 */
  locationBuildCode: string
  /** 房间号 */
  roomNum: string
  /** 宿舍楼名称 */
  locationBuildName: string
  /** 身高 */
  height: number
  /** 体重 */
  weight: number
  /** 入团日期 */
  leagueDate: string
  /** 入党日期 */
  partyDate: string
  /** 个人简历 */
  resume: string | null
  /** 出生地区 */
  csdq: string
  /** 出生地区名称 */
  csdqmc: string
  /** 出生地区(省份) */
  cssf: string
  /** 出生地区省份名称 */
  cssfmc: string
  /** 出生地区(城市) */
  cscs: string
  /** 出生地区城市名称 */
  cscsmc: string
  /** 账户地址 */
  accountAddress: string
  /** 健康状况代码 */
  healthStatusCode: string
  /** 健康状况名称 */
  healthStatusName: string
  /** 现住地区 */
  zzdq: string
  /** 现住地区名称 */
  areaName: string
  /** 现住地区(城市) */
  zzdqcs: string
  /** 现住地区城市名称 */
  zzdqcsmc: string
  /** 现住地区(省份) */
  zzdqsf: string
  /** 现住地区省份名称 */
  zzdqsfmc: string
  /** 户籍类别代码 */
  locationCategoryCode: string
  /** 户籍类别名称 */
  locationCategoryName: string
  /** 出生地区信息 */
  birthLocation: string[]
  /** 出生地区名称，如"四川省|广安市|邻水县" */
  birthLocationName: string
  /** 生源地区信息 */
  originLocation: string[]
  /** 生源地区名称，如"四川省|广安市|邻水县" */
  originLocationName: string
  /** 户口所在地信息 */
  accountsLocation: string[]
  /** 户口所在地名称，如"四川省|广安市|邻水县" */
  accountsLocationName: string
  /** 家庭所在地信息 */
  homeLocation: string[]
  /** 家庭所在地名称，如"四川省|广安市|邻水县" */
  homeLocationName?: string
  /** 学生生源地信息 */
  stuOriginLocation: string[]
  /** 学生生源地名称，如"福建省|泉州市|晋江市" */
  stuOriginLocationName?: string
  /** 区域地理信息 */
  areaLocation: string[]
  /** 姓名拼音 */
  namePY: string
  /** 曾用名 */
  formerName: string
  /** 是否独生子女 */
  isAlone: string
  /** 户口派出所 */
  accountPoliceStation: string
  /** 港澳台侨外代码 */
  hmtCode: string
  /** 港澳台侨外名称 */
  hmtName: string
  /** 国籍代码 */
  nationalityCode: string
  /** 国籍名称 */
  nationalityName: string
  /** 现家庭住址省份编码 */
  hkszsf: string
  /** 现家庭住址城市编码 */
  hkszcs: string
  /** 现家庭住址区县编码 */
  hkszdq: string
  /** 现家庭住址省份名称 */
  kkszsfmc: string
  /** 现家庭住址城市名称 */
  kkszcsmc: string
  /** 现家庭住址区县名称 */
  stuOriginCountyName: string
  /** 现家庭住址编码数组 */
  currentHomeLocation?: string[]
  /** 现家庭住址名称 */
  currentHomeLocationName?: string
}

export interface StudentInfoResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 学生信息数据 */
  data: StudentInfo
}

// 成绩查询请求参数
export interface ScoreQueryParams {
  page: number
  pageSize: number
  semesters: string[]
  ssbjmc?: string // 班级名称
  kcmc?: string // 课程名称
  kcxz?: string // 课程性质
  zdjsxm?: string // 指导教师姓名
}

// 成绩项
export interface ScoreItem {
  id: number
  jxrwid: number // 教学任务ID
  rccjlxdm: string // 日常成绩类型代码
  rccjlxmc: string // 日常成绩类型名称
  rccjdjbh: number // 日常成绩登记编号
  rccjkssj: string // 日常成绩考试时间
  rccjkszc: number // 日常成绩考试周次
  rccjsm: string // 日常成绩说明
  rccjtjsj: string // 日常成绩提交时间
  cjtjbz: number // 成绩提交标志
  cjptlx: string // 成绩评图类型
  remark: string // 备注
  create_time: number // 创建时间
  update_time: number // 更新时间
  deltag: number // 删除标记
  oprybh: string // 操作人员编号
  rccjcxzt: number // 日常成绩查询状态
  work_id: number // 工作ID
  release_time: string // 发布时间
  rccjdjid: number // 日常成绩登记ID
  xsxh: string // 学生学号
  xm: string // 学生姓名
  xkxxid: number | null // 选课信息ID
  xn: string // 学年
  xq: number // 学期
  ssbjmc: string // 所属班级名称
  zdjs: string // 指导教师
  zdjsxm: string // 指导教师姓名
  kcdm: string // 课程代码
  kcmc: string // 课程名称
  kcxz: string // 课程性质
  kcxf: number // 课程学分
  cjpdlx: string // 成绩评定类型
  cj: number // 成绩
  bfzcj: number // 百分制成绩
  tgbz: number // 通过标志
  cjjd: string // 成绩绩点
  cjsx: string // 成绩属性
  cjtjry: string // 成绩提交人员
  cjtjsj: string // 成绩提交时间
  semesters: string // 学期组合
  comprehensiveScore?: number // 综合成绩
  courseGpa?: string // 课程GPA
}

// 成绩统计信息
export interface ScoreStatistics {
  rws: number // 任务数
  cjs: number // 成绩数
  pjf: number // 平均分
  bjgs: number // 班级个数
  pageData: any[] // 页面数据
  typeData: any[] // 类型数据
}

// 成绩查询响应
export interface ScoreResponse {
  items: ScoreItem[]
  statistics: ScoreStatistics
  total: number
}

export interface StudentTotalScoreQuery {
  page: number
  pageSize: number
  semesters: string[]
  courseName?: string
  teacherName?: string
  courseCredit?: string
  scoreType?: string
  score?: string
  scoreGpa?: string
  scoreAttr?: string
  passed?: string
  submitTime?: string[]
}

export interface StudentTotalScoreItem {
  id: number
  ssbj: string
  className: string
  studentCode: string
  zwh: number
  studentName: string
  jxrwid: number
  xkxxid: number | null
  studyYear: string
  studyTerm: string
  zdjs: string
  teacherName: string
  kcdm: string
  courseName: string
  kcxz: string
  courseCredit: number
  scoreType: string
  score: number
  percentageScore: number
  passed: number
  scoreGpa: string
  scoreAttr: string
  isSubmit: number
  create_time: number
  update_time: number
  deltag: number
  cjtjry: string
  submitTime: string
  semesters: string
}

export interface StudentTotalScoreResponse {
  items: StudentTotalScoreItem[]
  query: Record<string, any>
  total: number
}

/**
 * 保存学生信息的参数
 */
export interface SaveStudentInfoParams {
  /** 学号 */
  studentCode: string
  /** 学生姓名 */
  studentName: string
  /** 考生号 */
  ticketCode: string
  /** 院系名称 */
  deptName: string
  /** 专业名称 */
  majorName: string
  /** 班级名称 */
  className: string
  /** 入学时间 */
  startStudyDate: string
  /** 学生状态 */
  stuStatus: string
  /** 学籍状态 */
  stuSchoolStatus: string
  /** 头像URL */
  avatarUrl: string
  /** 出生日期 */
  birthDate: string
  /** 证件类型代码 */
  IDTypeCode: string
  /** 证件类型名称 */
  IDTypeName: string
  /** 证件号码 */
  IDNumber: string
  /** 出生地编码数组 */
  birthLocation: string[]
  /** 籍贯地编码数组 */
  originLocation: string[]
  /** 政治面貌代码 */
  politicalOutlookCode: string
  /** 政治面貌名称 */
  politicalOutlookName: string
  /** 国籍代码 */
  nationalityCode: string
  /** 国籍名称 */
  nationalityName: string
  /** 港澳台侨外代码 */
  hmtCode: string
  /** 港澳台侨外名称 */
  hmtName: string
  /** 健康状况代码 */
  healthStatusCode: string
  /** 健康状况名称 */
  healthStatusName: string
  /** 出生地名称，如"四川省|广安市|邻水县" */
  birthLocationName: string
  /** 籍贯地名称，如"四川省|广安市|邻水县" */
  originLocationName: string
  /** 学历代码 */
  educationalCode?: string
  /** 学历名称 */
  educationalName?: string
  /** 家庭位置编码数组 */
  homeLocation?: string[]
  /** 家庭位置名称，如"四川省|广安市|邻水县" */
  homeLocationName?: string
  /** 现家庭住址省份编码 */
  hkszsf?: string
  /** 现家庭住址城市编码 */
  hkszcs?: string
  /** 现家庭住址区县编码 */
  hkszdq?: string
  /** 现家庭住址省份名称 */
  kkszsfmc?: string
  /** 现家庭住址城市名称 */
  kkszcsmc?: string
  /** 现家庭住址区县名称 */
  stuOriginCountyName?: string
  /** 现家庭住址编码数组 */
  currentHomeLocation?: string[]
  /** 现家庭住址名称 */
  currentHomeLocationName?: string
  /** 生源地区编码 */
  sydq?: string
  /** 生源地区名称 */
  sydqmc?: string
  /** 生源地区省份编码 */
  sydqsf?: string
  /** 生源地区省份名称 */
  sydqsfmc?: string
  /** 生源地区城市编码 */
  sydqcs?: string
  /** 生源地区城市名称 */
  sydqcsmc?: string
  /** 学生生源地信息数组 */
  stuOriginLocation?: string[]
  /** 学生生源地名称，如"福建省|泉州市|晋江市" */
  stuOriginLocationName?: string
  /** 保存信息类型 */
  type: 'base_info' | 'additional_info' | 'contact_info'
}

/**
 * 周次信息项
 */
export interface WeekItem {
  /** 周次的键值 */
  key: number
  /** 周次的值 */
  zc: number
}

/**
 * 周次信息响应
 */
export interface WeekResponse {
  /** 当前周次 */
  dqz: number
  /** 周次列表 */
  weekList: WeekItem[]
}

/**
 * 学籍异动申请查询参数接口
 */
export interface SchoolChangeApplyQuery {
  /** 页码 */
  page: number
  /** 每页记录数 */
  pageSize: number
  /** 学期数组，格式为"学年|学期" */
  semesters: string[]
  /** ID筛选 */
  id?: string
  /** 变更类型名称筛选 */
  changeTypeName?: string
  /** 变更原因名称筛选 */
  changeReasonName?: string
  /** 变更原因详情筛选 */
  changeReasonDetails?: string
  /** 申请时间筛选 */
  applicationTime?: string
  /** 辅导员审批筛选 */
  counselorApproval?: string
  /** 系部审批筛选 */
  deptApproval?: string
  /** 教务处审批筛选 */
  academicAffairsApproval?: string
}

/**
 * 学籍异动申请项目接口
 */
export interface SchoolChangeApplyItem {
  /** ID */
  id: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: string
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作用户 */
  operatorUser: string
  /** 申请类型 */
  applicationType: string
  /** 学生学号 */
  studentCode: string
  /** 学生姓名 */
  studentName: string
  /** 申请时间 */
  applicationTime: string
  /** 申请描述 */
  applicationDescription: string
  /** 附件列表 */
  attachmentList: string
  /** 辅导员审批: 0-待审批, 1-通过, 2-驳回, -1-拒绝 */
  counselorApproval: number
  /** 系部审批: 0-待审批, 1-通过, 2-驳回, -1-拒绝 */
  deptApproval: number
  /** 系部领导审批: 0-待审批, 1-通过, 2-驳回, -1-拒绝 */
  deptLeaderApproval: number
  /** 教务处审批: 0-待审批, 1-通过, 2-驳回, -1-拒绝 */
  academicAffairsApproval: number
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: string
  /** 变更类型代码 */
  changeType: string
  /** 变更类型名称 */
  changeTypeName: string
  /** 变更原因代码 */
  changeReasonCode: string
  /** 变更原因名称 */
  changeReasonName: string
  /** 变更原因详情 */
  changeReasonDetails: string
  /** 辅导员审批颜色 */
  counselorApprovalColor?: string
  /** 系部审批颜色 */
  deptApprovalColor?: string
  /** 教务处审批颜色 */
  academicAffairsApprovalColor?: string
}

/**
 * 学籍异动申请响应接口
 */
export interface SchoolChangeApplyResponse {
  /** 学籍异动申请列表 */
  items: SchoolChangeApplyItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
}

/**
 * 学籍异动申请文件项目接口
 */
export interface SchoolChangeApplyFileItem {
  /** 文件名称 */
  name: string
  /** 文件下载URL */
  url: string
}

/**
 * 学籍异动申请文件列表响应接口
 */
export interface SchoolChangeApplyFilesResponse {
  /** 响应状态码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应时间戳 */
  time: number
  /** 文件列表数据 */
  data: SchoolChangeApplyFileItem[]
}

/**
 * 学籍异动申请保存参数接口
 */
export interface SchoolChangeApplySaveParams {
  /** 申请ID（编辑时需要） */
  id?: number
  /** 备注 */
  remark?: string
  /** 创建时间 */
  create_time?: string
  /** 更新时间 */
  update_time?: number
  /** 删除标记 */
  deltag?: number
  /** 操作用户 */
  operatorUser?: string
  /** 申请类型 */
  applicationType?: string
  /** 学生代码 */
  studentCode?: string
  /** 学生姓名 */
  studentName?: string
  /** 申请时间 */
  applicationTime?: string
  /** 申请描述 */
  applicationDescription?: string
  /** 辅导员审批状态 */
  counselorApproval?: number
  /** 系部审批状态 */
  deptApproval?: number
  /** 系部领导审批状态 */
  deptLeaderApproval?: number
  /** 教务处审批状态 */
  academicAffairsApproval?: number
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: string
  /** 变更类型代码 */
  changeType: string
  /** 变更原因代码 */
  changeReasonCode: string
  /** 变更原因详情 */
  changeReasonDetails: string
  /** 变更类型名称 */
  changeTypeName: string
  /** 变更原因名称 */
  changeReasonName: string
  /** 附件列表，格式为"文件路径|文件名称" */
  attachmentList: string
  /** 行键（用于表格标识） */
  _X_ROW_KEY?: string
}

/**
 * 学籍异动申请保存响应接口
 */
export interface SchoolChangeApplySaveResponse {
  /** 响应状态码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应时间戳 */
  time: number
  /** 响应数据 */
  data: {
    /** 申请ID */
    id?: number
    /** 是否成功 */
    success?: boolean
    /** 消息 */
    message?: string
  }
}

/**
 * 学习计划查询参数接口
 */
export interface StudyPlanQuery {
  /** 页码 */
  page: number
  /** 每页记录数 */
  pageSize: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方式 */
  sortOrder?: 'asc' | 'desc'
  /** 学期数组，格式为"学年|学期" */
  semesters: string[]
  /** 周次筛选 */
  zc?: number | null
  /** 班级名称筛选 */
  skbjmc?: string
  /** 课程名称筛选 */
  kcmc?: string
  /** 教师名称筛选 */
  skjsxm?: string
  /** 授课日期筛选 */
  skrq?: string[]
  /** 节次筛选 */
  jcshow?: string
  /** 场地名称筛选 */
  skcdmc?: string
  /** 授课模式名称筛选 */
  skfsmc?: string
  /** 授课内容筛选 */
  sknl?: string
  /** 作业提示筛选 */
  zyts?: string
  /** 作业内容筛选 */
  zynr?: string
  /** 学生确认状态筛选 */
  skjhxsqrzt?: string
  /** 学生确认人姓名筛选 */
  skjhxsqrxsxm?: string
  /** 学生确认日期筛选 */
  skjhxsqrsj?: string[]
  /** 教师确认状态筛选 */
  skjhjsqrzt?: string
  /** 教学计划状态筛选 */
  skjhzxzt?: string
  /** 周期筛选 */
  cycle?: string
}

/**
 * 学习计划单条记录接口
 */
export interface StudyPlanItem {
  /** ID */
  id: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 教学任务ID */
  jxrwid: number
  /** 周次 */
  zc: number
  /** 授课日期 */
  skrq: string
  /** 授课开始时间 */
  skkssj: string
  /** 授课结束时间 */
  skjssj: string
  /** 星期几 */
  xqs: number
  /** 节次 */
  jc: string
  /** 节次显示 */
  jcshow: string
  /** 授课内容 */
  sknl: string
  /** 授课方式代码 */
  skfs: string
  /** 授课方式名称 */
  skfsmc: string
  /** 作业提示 */
  zyts: number
  /** 作业评估方式 */
  zypgfs: string
  /** 作业评估方式名称 */
  zypgfsmc: string
  /** 负责人是否在场 */
  fzrszs: string
  /** 使用仪器设备数量 */
  syyqsbsl: string
  /** 授课场地类型 */
  skcdlx: string
  /** 授课场地代码 */
  skcddm: string
  /** 授课场地名称 */
  skcdmc: string
  /** 授课教师代码 */
  skjs: string
  /** 授课教师姓名 */
  skjsxm: string
  /** 授课班级代码 */
  skbj: string
  /** 授课班级名称 */
  skbjmc: string
  /** 实用人数 */
  syrs: number
  /** 课时 */
  ks: number
  /** 学时 */
  xz: number
  /** 关联ID */
  glid: number
  /** 教学计划执行状态 */
  skjhzxzt: number
  /** 教学计划管理人确定状态 */
  skjhgzlrdzt: number
  /** 作业内容 */
  zynr: string
  /** 教学计划教师确认状态 */
  skjhjsqrzt: number
  /** 教学计划教师确认时间 */
  skjhjsqrsj: string
  /** 教学计划教师确认人编号 */
  skjhjsqrrybh: string
  /** 教学计划教师确认人姓名 */
  skjhjsqrryxm: string
  /** 教学计划学生确认状态 */
  skjhxsqrzt: number
  /** 教学计划学生确认时间 */
  skjhxsqrsj: string
  /** 教学计划学生确认学生学号 */
  skjhxsqrxsxh: string
  /** 教学计划学生确认学生姓名 */
  skjhxsqrxsxm: string
  /** 迟到申报状态 */
  cdsbzt: number
  /** 迟到申报事由情况 */
  cdsbsyqk: string
  /** 审批状态 */
  spzt: number
  /** 网络教学关联ID */
  wljxglid: number
  /** 学生分组ID */
  xsfzid: number
  /** 学生分组名称 */
  xsfzmc: string
  /** 附件列表 */
  fjlb: string
  /** 考勤照片 */
  kqzp: string
  /** 特提拔课申请状态 */
  ttbksqzt: number
  /** 处室确认审核状态 */
  csqrshzt: number
  /** 作业附件列表 */
  zyfjlb: string
  /** 直播平台代码 */
  zbptdm: string
  /** 直播平台名称 */
  zbptmc: string
  /** 直播平台内容 */
  zbptnr: string
  /** 教学平台代码 */
  jxptdm: string
  /** 教学平台名称 */
  jxptmc: string
  /** 教学平台内容 */
  jxptnr: string
  /** 学生上课审核状态 */
  xsskshzt: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 指定教师 */
  zdjs: string
  /** 指定教师姓名 */
  zdjsxm: string
  /** 学期组合,格式为"学年|学期" */
  semesters?: string
  /** 周期 */
  cycle?: string
  /** 学生确认状态颜色 */
  studentConfirmStatusColor?: string
  /** 教师确认状态颜色 */
  teacherConfirmStatusColor?: string
  /** 教学计划状态颜色 */
  teachingPlanStatusColor?: string
}

/**
 * 学习计划统计信息
 */
export interface StudyPlanStatistics {
  /** 总课时 */
  totalClassHour: number
  /** 本周总课时 */
  totalWeekClassHour: number
  /** 今日课时 */
  todayHour: number
  /** 本月课时 */
  monthHour: number
  /** 已上课时 */
  nowHour: number
  /** 当前未上课时 */
  nowNoHour: number
  /** 未上课时 */
  noHour: number
}

/**
 * 学习计划响应接口
 */
export interface StudyPlanResponse {
  /** 学习计划列表 */
  items: StudyPlanItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
  /** 当前周次 */
  week?: number
  /** 学年 */
  studyYear?: string
  /** 学期 */
  studyTerm?: string
  /** 统计信息 */
  statistics?: StudyPlanStatistics
}

/**
 * 学生德育查询参数接口
 */
export interface MoralEduQuery {
  /** 页码 */
  page: number
  /** 每页记录数 */
  pageSize: number
  /** 排序字段 */
  sortBy: string
  /** 排序方式 */
  sortOrder: 'asc' | 'desc'
  /** 学期数组，格式为"学年|学期" */
  semesters: string[]
  /** 自评分筛选 */
  selfRatingScore?: string
  /** 互评分筛选 */
  mutualScore?: string
  /** 活动分筛选 */
  activityScore?: string
  /** 明细分筛选 */
  detailScore?: string
  /** 德育排名筛选 */
  moralEducationRank?: string
  /** 学业分筛选 */
  gradeScore?: string
  /** 学业排名筛选 */
  gradeRank?: string
  /** 综测分筛选 */
  comprehensiveEvaluationScore?: string
  /** 综测排名筛选 */
  comprehensiveEvaluationRank?: string
  /** 德育分筛选 */
  moralEducationScore?: string
}

/**
 * 学生德育记录项接口
 */
export interface MoralEduItem {
  /** ID */
  id: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 学生学号 */
  studentCode: string
  /** 学生姓名 */
  studentName: string
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: number
  /** 注册状况 */
  zczk: string
  /** 注册日期 */
  zcrq: string | null
  /** 报到日期 */
  bdrq: string | null
  /** 是否离散退档 */
  sflstd: number
  /** 异动日期 */
  ydrq: string | null
  /** 异动原因 */
  ydyy: string
  /** 未注册原因代码 */
  wzcyydm: string | null
  /** 未注册原因 */
  wzcyy: string
  /** 未注册补档期限 */
  wzcbdqx: string
  /** 是否请假 */
  sfqj: number
  /** 请假理由 */
  qjly: string
  /** 操作人员 */
  czry: string | null
  /** 操作人姓名 */
  czrxm: string | null
  /** 学生自修体系填写次数 */
  xszxtxyczcs: number
  /** 德育分 */
  moralEducationScore: number
  /** 德育补偿分 */
  moralEducationCompensateScore: string
  /** 自评分 */
  selfRatingScore: string
  /** 互评分 */
  mutualScore: string
  /** 活动分 */
  activityScore: string
  /** 明细分 */
  detailScore: string
  /** 德育排名 */
  moralEducationRank: number
  /** 学业分 */
  gradeScore: string
  /** 学业排名 */
  gradeRank: number
  /** 综测分 */
  comprehensiveEvaluationScore: string
  /** 综测排名 */
  comprehensiveEvaluationRank: number
  /** 奖学金等级 */
  scholarshipLevel: string
  /** 创新培养 */
  cxpy: string
  /** 创新等级代码 */
  cxdjdm: string
  /** 创新等级名称 */
  cxdjmc: string
  /** 培养责任代码 */
  pyczrdm: string
  /** 培养责任名称 */
  pyczrmc: string
  /** 家长意见反馈 */
  jzyjfk: string
  /** 家长意见反馈时间 */
  jzyjfksj: string | null
  /** 家长意见反馈人 */
  jzyjfkr: number
}

/**
 * 学生德育响应接口
 */
export interface MoralEduResponse {
  /** 德育记录列表 */
  items: MoralEduItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
}

/**
 * 学生家庭成员查询参数
 */
export interface StudentFamilyQuery {
  /** 页码 */
  page: number
  /** 每页记录数 */
  pageSize: number
  /** 排序字段 */
  sortBy: string
  /** 排序方式 */
  sortOrder: 'asc' | 'desc'
  /** 家庭成员关系筛选 */
  familyMemberRelationship?: string
  /** 家庭成员姓名筛选 */
  familyMemberName?: string
  /** 家庭成员出生日期筛选 */
  familyMemberBirthdate?: string
  /** 证件号码筛选 */
  IDNumber?: string
  /** 政治面貌筛选 */
  familyMemberPoliticalStatus?: string
  /** 工作职位筛选 */
  workPosition?: string
  /** 电话号码筛选 */
  phoneNumber?: string
  /** 是否监护人筛选 */
  guardian?: string
}

/**
 * 学生家庭成员信息
 */
export interface StudentFamilyMember {
  /** ID */
  id: number
  /** 用户名/学号 */
  userName: string
  /** 家庭成员姓名 */
  familyMemberName: string
  /** 家庭成员性别: 0-女, 1-男 */
  familyMemberGender: number
  /** 家庭成员出生日期 */
  familyMemberBirthdate: string
  /** 家庭成员关系代码 */
  familyMemberRelationshipCode: string
  /** 家庭成员关系名称 */
  familyMemberRelationship: string
  /** 工作职位 */
  workPosition: string
  /** 联系电话 */
  phoneNumber: string
  /** 证件号码 */
  IDNumber: string
  /** 健康状况代码 */
  healthStatusCode: string
  /** 政治面貌 */
  familyMemberPoliticalStatus: string
  /** 是否监护人: 0-否, 1-是 */
  isGuardian: string
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作人员编号 */
  oprybh: string
  /** 应用token */
  apptoken: string
  /** 融云token */
  rongtoken: string
  /** 最后登录时间 */
  last_login_time: number
  /** 登录次数 */
  login_count: number
  /** 最后登录IP */
  last_login_ip: string
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 国籍代码 */
  gjdqdm: string
  /** 国籍名称 */
  gjdqmc: string
  /** 证件类型 */
  zjlx: string
  /** 证件类型名称 */
  zjlxmc: string
  /** 民族代码 */
  mzdm: string
  /** 民族名称 */
  mzmc: string
  /** 最高学历代码 */
  zgxldm: string
  /** 最高学历名称 */
  zgxlmc: string
  /** 工作单位职位信息 */
  gzdwzwxx: string
  /** 审核状态 */
  shzt: number
  /** 附件类别 */
  fjlb: string
  /** 是否监护人(中文) */
  guardian: string
}

/**
 * 学生家庭成员响应
 */
export interface StudentFamilyResponse {
  /** 家庭成员列表 */
  items: StudentFamilyMember[]
  /** 总记录数 */
  total: number
}

/**
 * 学生德育记录查询参数
 */
export interface MoralEduRecordQuery {
  /** 页码 */
  page: number
  /** 每页记录数 */
  pageSize: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方式 */
  sortOrder?: 'asc' | 'desc'
  /** 学期，格式为"学年|学期"，例如："2024-2025|1" */
  semesters: string
}

/**
 * 学生德育记录项接口
 */
export interface MoralEduRecordItem {
  /** ID */
  id: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 学生学号 */
  studentCode: string
  /** 学生姓名 */
  studentName: string
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: number
  /** 德育分 */
  moralEducationScore: number
  /** 自评分 */
  selfRatingScore: string
  /** 互评分 */
  mutualScore: string
  /** 活动分 */
  activityScore: string
  /** 明细分 */
  detailScore: string
  /** 德育排名 */
  moralEducationRank: number
  /** 学业分 */
  gradeScore: string
  /** 学业排名 */
  gradeRank: number
  /** 综测分 */
  comprehensiveEvaluationScore: string
  /** 综测排名 */
  comprehensiveEvaluationRank: number
  /** 学期组合,格式为"学年|学期" */
  semesters: string
}

/**
 * 学生德育记录响应接口
 */
export interface MoralEduRecordResponse {
  /** 德育记录列表 */
  items: MoralEduRecordItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
}

/**
 * 学生课程表提交请求参数
 */
export interface StudentScheduleSubmitParams {
  /** 教学任务ID */
  jxrwid: number
  /** 记录ID */
  id: number
}

/**
 * 学生课程表提交响应
 */
export interface StudentScheduleSubmitResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 前端消息 */
  frontMsg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: null | any
}

/**
 * 申请打印在学证明响应
 */
export interface ApplyLearningProveResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: boolean
}

/**
 * 申请打印毕业证明参数
 */
export interface ApplyGraduateProveParams {
  /** 操作类型：apply_normal-正常申请，apply-入伍申请 */
  optype: 'apply_normal' | 'apply'
  /** 附件列表，入伍申请时必填 */
  fjlb?: string
}

/**
 * 申请打印毕业证明响应
 */
export interface ApplyGraduateProveResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: boolean
}

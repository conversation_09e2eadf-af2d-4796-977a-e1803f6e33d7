import type { ScheduleItem } from '@/types/schedule'

/**
 * 将表格数据转换为列表数据
 * @param list 课程列表
 * @param weekDays 星期信息
 * @param sectionTimes 时间段信息
 */
export function tableToListData(
  list: ScheduleItem[],
  weekDays: Array<{ day: string; date: string; dayIndex: number }>,
  sectionTimes: Array<{ section: number[]; time: string }>,
) {
  // 按星期分组
  const groupedData = weekDays.map((day) => {
    // 获取当天的所有课程
    const dayCourses = list.filter((item) => item.DayIndex === day.dayIndex)

    // 按mergeTaskId和上课时间段分组
    const mergedCourses = new Map<string, ScheduleItem[]>()
    dayCourses.forEach((item) => {
      // 使用mergeTaskId和上课时间段作为key
      const key = `${item.mergeTaskId}-${item.startNode}-${item.endNode}`
      if (!mergedCourses.has(key)) {
        mergedCourses.set(key, [item])
      } else {
        mergedCourses.get(key)?.push(item)
      }
    })

    // 转换为课程列表并按时间排序
    const courses = Array.from(mergedCourses.values())
      .map((items) => {
        const item = items[0]
        // 如果是合班，合并班级名称
        const className =
          items.length > 1 ? items.map((i) => i.className).join('、') : item.className

        return {
          time: `${item.jckssj.substring(0, 5)}-${item.jcjssj.substring(0, 5)}` || '',
          period: `${item.sectionsShow}节`,
          name: item.course,
          teacher: item.teacherName,
          location: item.spaceName,
          className,
          dayOfWeek: item.DayIndex,
          section: [Number(item.startNode), Number(item.endNode)],
          startNode: Number(item.startNode), // 添加startNode用于排序
          xz: item.xz, // 性质（正常/调课/补课等）
          spzt: item.spzt, // 审批状态
        }
      })
      // 按开始节次排序
      .sort((a, b) => a.startNode - b.startNode)

    return {
      day: day.day,
      date: day.date,
      courses,
    }
  })

  return groupedData
}

/**
 * 将列表数据转换为表格数据
 * @param list 课程列表
 */
export function listToTableData(
  list: Array<{
    day: string
    date: string
    courses: Array<{
      time: string
      period: string
      name: string
      teacher: string
      location: string
      className: string
      dayOfWeek: number
      section: number[]
      xz?: string
      spzt?: number
    }>
  }>,
) {
  // 将列表数据转换为扁平化的表格数据
  return list.flatMap((day) =>
    day.courses.map((course) => ({
      ...course,
      day: day.day,
      date: day.date,
    })),
  )
}

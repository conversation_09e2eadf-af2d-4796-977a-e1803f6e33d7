import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { SchoolChangeApplyItem } from '@/types/student'

/**
 * 学籍异动编辑数据接口
 */
export interface SchoolChangeEditData {
  /** 申请ID */
  id: number
  /** 异动申请学期 */
  semester: string
  /** 异动类型 */
  changeType: string
  /** 异动类型代码 */
  changeTypeCode: string
  /** 异动原因 */
  changeReason: string
  /** 异动原因代码 */
  changeReasonCode: string
  /** 异动原因补充 */
  changeReasonDetails: string
  /** 相关附件 */
  attachments: Array<{ url: string; name: string }>
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: string
  /** 原始数据（用于对比） */
  originalData: SchoolChangeApplyItem
}

export const useSchoolChangeStore = defineStore(
  'schoolChange',
  () => {
    // 当前编辑的学籍异动数据
    const currentEditData = ref<SchoolChangeEditData | null>(null)

    // 是否为编辑模式
    const isEditMode = ref(false)

    // 是否为查看模式（只读）
    const isViewMode = ref(false)

    /**
     * 设置编辑数据
     * @param data 学籍异动申请数据
     */
    const setEditData = (data: SchoolChangeApplyItem) => {
      // 解析附件数据
      let attachments: Array<{ url: string; name: string }> = []
      if (data.attachmentList) {
        try {
          // 附件格式为 "文件路径|文件名称,文件路径|文件名称"
          attachments = data.attachmentList
            .split(',')
            .map((item) => {
              const [url, name] = item.split('|')
              return { url: url || '', name: name || '' }
            })
            .filter((item) => item.url && item.name)
        } catch (error) {
          console.warn('解析附件数据失败:', error)
          attachments = []
        }
      }

      currentEditData.value = {
        id: data.id,
        semester: `${data.studyYear}学年 第${data.studyTerm}学期`,
        changeType: data.changeTypeName,
        changeTypeCode: data.changeType,
        changeReason: data.changeReasonName,
        changeReasonCode: data.changeReasonCode,
        changeReasonDetails: data.changeReasonDetails || '',
        attachments,
        studyYear: data.studyYear,
        studyTerm: data.studyTerm,
        originalData: data,
      }

      isEditMode.value = true
    }

    /**
     * 设置查看模式
     * @param viewMode 是否为查看模式
     */
    const setViewMode = (viewMode: boolean) => {
      isViewMode.value = viewMode
      if (viewMode) {
        isEditMode.value = false
      }
    }

    /**
     * 清除编辑数据
     */
    const clearEditData = () => {
      currentEditData.value = null
      isEditMode.value = false
      isViewMode.value = false
    }

    /**
     * 获取当前编辑数据
     */
    const getCurrentEditData = () => {
      return currentEditData.value
    }

    /**
     * 检查是否为编辑模式
     */
    const getIsEditMode = () => {
      return isEditMode.value
    }

    /**
     * 检查是否为查看模式
     */
    const getIsViewMode = () => {
      return isViewMode.value
    }

    /**
     * 更新编辑数据的某个字段
     * @param field 字段名
     * @param value 字段值
     */
    const updateEditField = (field: keyof SchoolChangeEditData, value: any) => {
      if (currentEditData.value) {
        ;(currentEditData.value as any)[field] = value
      }
    }

    return {
      currentEditData,
      isEditMode,
      isViewMode,
      setEditData,
      setViewMode,
      clearEditData,
      getCurrentEditData,
      getIsEditMode,
      getIsViewMode,
      updateEditField,
    }
  },
  {
    persist: {
      storage: {
        getItem: uni.getStorageSync,
        setItem: uni.setStorageSync,
      },
      paths: ['currentEditData', 'isEditMode', 'isViewMode'],
    },
  },
)

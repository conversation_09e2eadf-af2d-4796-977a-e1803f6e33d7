<template>
  <wd-popup
    v-model="visible"
    position="bottom"
    :close-on-click-modal="true"
    custom-style="height: 80vh; border-radius: 16px 16px 0 0;"
    @close="handleClose"
  >
    <view class="popup-header px-4 py-3 border-b border-gray-100">
      <view class="text-gray-500" @click="handleCancel">取消</view>
      <view class="text-lg font-semibold">{{ title }}</view>
      <view v-if="multiple || useArrangeApi" class="text-blue-500" @click="handleConfirm">
        确认
      </view>
      <view v-else class="text-blue-500" @click="handleClose">确认</view>
    </view>
    <view class="popup-content">
      <VenueSelector
        ref="venueSelectorRef"
        :multiple="multiple"
        :selected-venues="selectedVenues"
        :use-arrange-api="useArrangeApi"
        :jxrwid="jxrwid"
        :skjhid="skjhid"
        @select="handleSingleSelect"
        @multi-select="handleMultiSelect"
      />
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import VenueSelector from './index.vue'
import type { Venue } from '@/types/venue'

// 定义组件属性
interface Props {
  /** 是否显示弹窗 */
  modelValue: boolean
  /** 是否多选模式 */
  multiple?: boolean
  /** 弹窗标题 */
  title?: string
  /** 已选择的场地（多选模式） */
  selectedVenues?: Venue[]
  /** 是否使用授课安排场地接口 */
  useArrangeApi?: boolean
  /** 教学任务ID（使用授课安排接口时必需） */
  jxrwid?: number
  /** 授课计划ID（使用授课安排接口时必需） */
  skjhid?: number
  /** 授课场地类型（使用授课安排接口时必需） */
  skcdlx?: string
}

// 定义组件事件
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'select', venue: Venue): void
  (e: 'multiSelect', venues: Venue[]): void
  (e: 'cancel'): void
  (e: 'confirm', venues: Venue[]): void
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  title: '选择授课场地',
  selectedVenues: () => [],
  useArrangeApi: false,
})

const emit = defineEmits<Emits>()

// 内部状态
const visible = ref(false)
const currentSelectedVenues = ref<Venue[]>([])
const venueSelectorRef = ref()

// 监听外部visible变化
watch(
  () => props.modelValue,
  async (newValue) => {
    visible.value = newValue
    if (newValue) {
      // 弹窗打开时，初始化选中状态
      currentSelectedVenues.value = [...props.selectedVenues]
      // 等待组件挂载后清空VenueSelector的选中状态
      await nextTick()
      if (venueSelectorRef.value && venueSelectorRef.value.clearAllSelection) {
        venueSelectorRef.value.clearAllSelection()
      }
    }
  },
  { immediate: true },
)

// 监听内部visible变化
watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理单选
const handleSingleSelect = (venue: Venue) => {
  if (!props.multiple) {
    if (props.useArrangeApi) {
      // 使用授课安排API时，单选也需要确认
      currentSelectedVenues.value = [venue]
    } else {
      // 普通模式直接选择并关闭
      emit('select', venue)
      handleClose()
    }
  }
}

// 处理多选
const handleMultiSelect = (venues: Venue[]) => {
  if (props.multiple) {
    currentSelectedVenues.value = venues
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  handleClose()
}

// 处理确认
const handleConfirm = async () => {
  if (props.useArrangeApi) {
    if (currentSelectedVenues.value.length === 0) {
      uni.showToast({
        title: '请先选择场地',
        icon: 'error',
      })
      return
    }

    // 验证必需参数
    if (!props.jxrwid || !props.skjhid) {
      console.error('缺少必需参数:', { jxrwid: props.jxrwid, skjhid: props.skjhid })
      uni.showToast({
        title: '参数错误，请重试',
        icon: 'error',
      })
      return
    }

    // 使用授课安排API时，调用安排接口
    try {
      const selectedVenue = currentSelectedVenues.value[0]

      const params = {
        selectcd: selectedVenue.siteCode,
        skjhid: Number(props.skjhid), // 确保转换为数字类型
        jxrwid: Number(props.jxrwid), // 确保转换为数字类型
        skcdlx: props.skcdlx || '01', // 使用传入的场地类型，默认为'01'
        opxx: '1', // 固定值
        hbld: '1', // 固定值
        sknl: '', // 固定值
      }

      console.log('场地安排参数:', params) // 添加调试日志

      // 调用安排接口
      const { arrangeTeachingSite } = await import('@/service/teacher')
      await arrangeTeachingSite(params)

      // 成功后触发事件
      if (props.multiple) {
        emit('confirm', currentSelectedVenues.value)
        emit('multiSelect', currentSelectedVenues.value)
      } else {
        emit('select', selectedVenue)
      }

      uni.showToast({
        title: '场地安排成功',
        icon: 'success',
      })
    } catch (error) {
      console.error('场地安排失败:', error)
      uni.showToast({
        title: '场地安排失败',
        icon: 'error',
      })
      return // 失败时不关闭弹窗
    }
  } else if (props.multiple) {
    // 普通多选模式
    emit('confirm', currentSelectedVenues.value)
    emit('multiSelect', currentSelectedVenues.value)
  }

  handleClose()
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  // 清空选中状态
  currentSelectedVenues.value = []
  // 清空VenueSelector的选中状态
  if (venueSelectorRef.value && venueSelectorRef.value.clearAllSelection) {
    venueSelectorRef.value.clearAllSelection()
  }
}
</script>

<style lang="scss" scoped>
// 弹窗样式
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100rpx;
  padding: 0 32rpx;
  font-size: 32rpx;
  font-weight: 500;
  background-color: #ffffff;
  border-bottom: 1px solid #f2f2f2;
}

.popup-content {
  height: calc(80vh - 60px);
  overflow: hidden;
}
</style>

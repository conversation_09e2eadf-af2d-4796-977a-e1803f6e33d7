<template>
  <view class="template-tip bg-blue-50 p-[16rpx] rounded-lg mb-[16rpx]" :class="tipClass">
    <text class="text-xs" :class="textClass">
      <slot>{{ text }}</slot>
    </text>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

// 提示类型定义
export type TipType = 'primary' | 'danger' | 'warning' | 'secondary' | 'default'

// Props定义
interface Props {
  /** 提示类型 */
  type?: TipType
  /** 提示文本 */
  text?: string
  /** 是否显示左侧边框 */
  showBorder?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  text: '',
  showBorder: true,
})

// 计算提示框样式类
const tipClass = computed(() => {
  const typeClasses: Record<TipType, string> = {
    primary: 'bg-blue-50',
    danger: 'bg-red-50',
    warning: 'bg-orange-50',
    secondary: 'bg-gray-50',
    default: 'bg-gray-50',
  }

  const borderClasses: Record<TipType, string> = {
    primary: 'border-l-blue-500',
    danger: 'border-l-red-500',
    warning: 'border-l-orange-500',
    secondary: 'border-l-gray-500',
    default: 'border-l-gray-500',
  }

  let classes = typeClasses[props.type]

  if (props.showBorder) {
    classes += ` border-l-6rpx border-l-solid ${borderClasses[props.type]}`
  }

  return classes
})

// 计算文本样式类
const textClass = computed(() => {
  const typeClasses: Record<TipType, string> = {
    primary: 'text-blue-600',
    danger: 'text-red-600',
    warning: 'text-orange-600',
    secondary: 'text-gray-600',
    default: 'text-gray-600',
  }

  return typeClasses[props.type]
})
</script>

<style lang="scss" scoped>
// 组件样式已通过UnoCSS类实现，无需额外样式
</style>

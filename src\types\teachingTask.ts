/**
 * 教学任务相关类型定义
 */

/**
 * 教学任务查询参数
 */
export interface TeachingTaskQueryParams {
  /** 当前页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方式 */
  sortOrder?: string
  /** 班级名称 */
  className?: string
  /** 课程代码 */
  courseCode?: string
  /** 课程名称 */
  courseName?: string
  /** 考核方式 */
  assessmentMethod?: string
  /** 课程总学时 */
  courseTotalHours?: string
  /** 周学时 */
  weekHours?: string
  /** 周数 */
  weeks?: string
  /** 工作量 */
  workloadNum?: string
  /** 提交状态 */
  submitStatus?: string
  /** 教材使用 */
  textbookUse?: string
  /** 学期数组，格式如：["2024-2025|2"] */
  semesters?: string[]
  /** 索引签名，允许添加任意字符串索引的属性 */
  [key: string]: unknown
}

/**
 * 教学任务查询参数 (新版API)
 */
export interface TeachingTaskQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 搜索关键词 */
  searchKeyword?: string
  /** 学期，格式：学年|学期，如：2024-2025|2 */
  semesters?: string
  /** 教材使用状态，0,1表示全部 */
  textbookUse?: string
  /** 索引签名，允许添加任意字符串索引的属性 */
  [key: string]: unknown
}

/**
 * 教学任务分页查询参数
 */
export interface TeachingTaskPaginationQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 搜索关键词 */
  searchKeyword?: string
}

/**
 * 分页信息
 */
export interface PageInfo {
  /** 总记录数 */
  total: number
  /** 每页数量 */
  pageSize: number
  /** 当前页码 */
  currentPage: number
  /** 是否有更多页 */
  hasMorePage: boolean
  /** 总页数 */
  totalPage: number
}

/**
 * 教学任务项
 */
export interface TeachingTaskItem {
  /** 合并任务ID */
  mergeTaskId?: number | null
  /** 任务ID */
  id: number
  /** 计划ID */
  planId: number
  /** 选课ID */
  selectCourseId: number | null
  /** 合班ID */
  combineCourseId: number | null
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: number
  /** 任务类型 */
  taskType: string
  /** 课程代码 */
  courseCode: string
  /** 课程名称 */
  courseName: string
  /** 课程类型 */
  courseType: string
  /** 是否主课程 */
  mainCourse: number
  /** 考核方式 */
  assessmentMethod: string
  /** 课程总学时 */
  courseTotalHours: string
  /** 教学学时 */
  teachingHours: string
  /** 实验学时 */
  experimentHours: string
  /** 上机学时 */
  computerHours: string
  /** 虚拟学时 */
  virtualHours: string
  /** 周学时 */
  weekHours: string
  /** 课程信息周学时 */
  weekHoursCourseInfo: string
  /** 周数 */
  weeks: number
  /** 课程信息周数 */
  weeksCourseInfo: number
  /** 学分 */
  creditHour: number
  /** 学校代码 */
  schoolCode: string
  /** 院系代码 */
  deptCode: string
  /** 院系名称 */
  deptName: string
  /** 教研室代码 */
  teachOfficeCode: string
  /** 教研室名称 */
  teachOfficeName: string
  /** 备注 */
  remark: string | null
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 主讲教师代码 */
  leaderTeacherCode: string
  /** 主讲教师姓名 */
  leaderTeacherName: string
  /** 其他教师姓名 */
  otherTeacherName: string
  /** 其他教师代码 */
  otherTeacherCode: string
  /** 班级代码 */
  classCode: string
  /** 班级名称 */
  className: string
  /** 任务类型状态 */
  taskTypeStatus: string
  /** 任务执行状态 */
  taskExecutionStatus: number
  /** 班级代表 */
  sectionRepresentative: string | null
  /** 班级代表姓名 */
  sectionRepresentativeName: string
  /** 是否开启签到 */
  checkingInOpen: number
  /** 是否考核 */
  isAssessment: number
  /** 考核类型 */
  assessmentType: string
  /** 提交状态 */
  submitStatus: string | number
  /** 教材使用 */
  textbookUse: string | number
  /** 操作人ID */
  operatorId: string
  /** 场地代码 */
  siteCode: string | null
  /** 场地名称 */
  siteName: string | null
  /** 开始周 */
  startWeek: number
  /** 结束周 */
  endWeek: number
  /** 教学信息 */
  teachingInfo: string
  /** 教学周 */
  teachingWeek: string
  /** 班级周 */
  classWeeks: string
  /** 学生知识 */
  studentKnowledge: string
  /** 后续课程知识 */
  followingCourseKnowledge: string
  /** 后续课程技能 */
  followingCourseSkill: string
  /** 教学大纲 */
  teachingOutline: string
  /** 知识目标 */
  knowledgeObjective: string
  /** 能力目标 */
  abilityObjective: string
  /** 素质目标 */
  qualityObjective: string
  /** 考核方式 */
  assessmentWay: string
  /** 配对教师 */
  pairTeacher: string
  /** 配对教师学时 */
  pairTeacherHours: string
  /** 工作量 */
  workloadNum: string
  /** 确认学时 */
  affirmHours: string
  /** 教学计划审批 */
  teachingPlanApproval: number
  /** 评价应有数量 */
  evaluationShouldNum: number
  /** 评价实际数量 */
  evaluationActualNum: number
  /** 评价平均分 */
  evaluationAverageScore: number | null
  /** 评价有效数量 */
  evaluationEffectiveNum: number
  /** 评价分数 */
  evaluationScore: string
  /** 是否提交按钮 */
  isSubmitButton: number
  /** 教学计划提交 */
  teachingPlanSubmit: number
  /** 教学计划状态 */
  teachingPlanStatus: number
  /** 课程标准附件 */
  courseStandardAttachment: string | null
  /** 是否锁定课程评价 */
  isCourseEvaluationLock: number
  /** 教师手册分析 */
  teacherManualAnalysis: string | null
  /** 工作簿提交状态 */
  workbookSubmitStatus: number
  /** 工作簿教研室审批 */
  workbookTeachOfficeApproval: number
  /** 工作簿院系审批 */
  workbookDeptApproval: number
  /** 是否排除考勤考试 */
  isExcludeAttendanceExam: number
  /** 教学方法 */
  teachingMethod: string
  /** 教学方法名称 */
  teachingMethodName: string
  /** 管理教学平台 */
  gljxpt: string
  /** 课程ID */
  course_id: number
  /** 班级ID */
  class_id: number
  /** 总课时 */
  classHoursTotal: string
  /** 周课时 */
  classHoursWeek: string
  /** 系数 */
  coefficient: string
  /** 考试方法 */
  examinationMethods: string
  /** 成绩录入状态 */
  scoreEntryStatus: number
  /** 成绩录入状态名称 */
  scoreEntryStatusName: string
  /** 学分 */
  credit: number
}

/**
 * 教学任务列表响应数据
 */
export interface TeachingTaskListResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: {
    /** 教学任务列表 */
    list: TeachingTaskItem[]
    /** 总数 */
    total: number
    /** 分页信息 */
    page: PageInfo
  }
}

/**
 * 教学任务列表响应 (新版API)
 */
export interface TeachingTaskResponse {
  /** 教学任务列表 */
  list: TeachingTaskItem[]
  /** 总记录数 */
  total: number
  /** 分页信息 */
  page: PageInfo
}

/**
 * 教学任务详情类型
 */
export interface TeachingTaskDetail {
  /** 课程ID */
  id: number
  /** 课程名称 */
  courseName: string
  /** 课程编码 */
  courseCode: string
  /** 班级名称 */
  className: string
  /** 学期 */
  semester: string
  /** 学年 */
  academicYear: string
  /** 总学时 */
  courseTotalHours: string
  /** 周学时 */
  weekHours: string
  /** 学分 */
  credit: number
  /** 周数 */
  weeks: string
  /** 课程状态 */
  taskExecutionStatus: number
  /** 考核方式 */
  assessmentMethod: string
  /** 系数 */
  coefficient: string
  /** 成绩提交状态 */
  submitStatus: string
  /** 教材使用状态 */
  textbookUse: string
}

/**
 * 教学任务通知查询参数
 */
export interface TeachingTaskNoticeQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方式 */
  sortOrder?: 'asc' | 'desc'
  /** 邮件主题 */
  yjzt?: string
  /** 收件人 */
  sjr?: string
  /** 索引签名，允许添加任意字符串索引的属性 */
  [key: string]: unknown
}

/**
 * 教学任务通知项
 */
export interface TeachingTaskNoticeItem {
  /** 通知ID */
  id: number
  /** 发件人编号 */
  fjr: string
  /** 发件人姓名 */
  fjrxm: string
  /** 发件人部门 */
  fjrbm: string
  /** 发件人部门名称 */
  fjrbmmc: string
  /** 发件时间 */
  fjsj: string
  /** 收件人，格式：学号|姓名,学号|姓名 */
  sjr: string
  /** 收件人名称 */
  sjrmc: string
  /** 邮件主题 */
  yjzt: string
  /** 邮件内容（HTML格式） */
  yjnr: string
  /** 邮件附件 */
  yjfj: string
  /** 邮件附件大小 */
  yjfjdx: number
  /** 主题类型 */
  zttp: string
  /** 模板类别 */
  tplb: string
  /** 文件列表，格式：文件名|URL */
  wjlb: string
  /** 删除标记 */
  deltag: number
  /** 备注 */
  remark: string
  /** 创建时间（时间戳） */
  create_time: number
  /** 更新时间（时间戳） */
  update_time: number
  /** 操作员编号 */
  oprybh: string
  /** 邮件类型 */
  yjlx: number
  /** 扩展字段1 */
  kzzd1: string
  /** 扩展字段2 */
  kzzd2: string
  /** 会议开始时间 */
  hykssj: string
  /** 会议结束时间 */
  hyjssj: string
  /** 微信推送标志 */
  wxtsbz: number
}

/**
 * 教学任务通知列表响应
 */
export interface TeachingTaskNoticeResponse {
  /** 通知列表 */
  items: TeachingTaskNoticeItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
}

/**
 * 教学任务课程安排请求参数
 */
export interface TeachingScheduleArrangementRequest {
  /** 教学任务ID */
  jxrwid: string
  /** 步骤 */
  step: number
}

/**
 * 教学任务课程安排课程信息
 */
export interface TeachingTaskCourseInfo {
  /** 任务ID */
  id: number
  /** 计划ID */
  ssjh: number
  /** 选课ID */
  ssxk: number | null
  /** 合班ID */
  sshb: number | null
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 任务类型 */
  rwlx: string
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 课程性质 */
  kcxz: string
  /** 是否主课程 */
  zgkc: number
  /** 考核方式 */
  khfs: string
  /** 课程总学时 */
  xqzxs: string
  /** 教学学时 */
  jkxs: string
  /** 实验学时 */
  syxs: string
  /** 上机学时 */
  sjxs: string
  /** 虚拟学时 */
  sxxs: string
  /** 周学时 */
  zxs: string
  /** 课程信息周学时 */
  zxspkxx: string
  /** 周数 */
  zs: number
  /** 课程信息周数 */
  zspkxx: number
  /** 学分 */
  kcxf: number
  /** 学校代码 */
  ssxy: string
  /** 院系代码 */
  ssxb: string
  /** 院系名称 */
  ssxbmc: string
  /** 教研室代码 */
  ssjys: string
  /** 教研室名称 */
  ssjysmc: string
  /** 备注 */
  remark: string | null
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 主讲教师代码 */
  zdjs: string
  /** 主讲教师姓名 */
  zdjsxm: string
  /** 其他教师姓名 */
  qtfdjsxm: string
  /** 其他教师代码 */
  qtfdjs: string
  /** 班级代码 */
  ssbj: string
  /** 班级名称 */
  bjmc: string
  /** 任务类型状态 */
  rwlxzt: string
  /** 任务执行状态 */
  zxzt: number
  /** 班级代表 */
  kdb: string | null
  /** 班级代表姓名 */
  kdbxm: string
  /** 是否开启签到 */
  kdbfzkqkf: number
  /** 是否考核 */
  sfkh: number
  /** 考核类型 */
  cjptlx: string
  /** 成绩提交状态 */
  cjtjbz: number
  /** 是否检查 */
  jcxybz: number
  /** 检查说明 */
  jcxysm: string
  /** 操作人ID */
  oprybh: string
  /** 场地代码 */
  skcddm: string | null
  /** 场地名称 */
  skcdmc: string | null
  /** 开始周 */
  ksz: number
  /** 结束周 */
  jsz: number
  /** 排课信息 */
  pkxx: string
  /** 教学周次 */
  jxzc: string
  /** 排课周次 */
  pkcz: string
  /** 必备知识技能 */
  bbzsjn: string
  /** 核心课程通过知识 */
  hxkctgzs: string
  /** 核心课程通过技能 */
  hxkctgjn: string
  /** 课程教学大纲信息 */
  kcjxdgxx: string
  /** 课程教学知识目标 */
  kcjyzsmb: string
  /** 课程教学能力目标 */
  kcjynlmb: string
  /** 课程教学素质目标 */
  kcjyszmb: string
  /** 课程考核比分 */
  kckhbf: string
  /** 教师手册分析 */
  jdzjs: string
  /** 教师手册学时 */
  jdzjsks: string
  /** 工作量系数 */
  gzlxs: string
  /** 人日学时 */
  yrdxs: string
  /** 教学计划审核 */
  skjhsh: number
  /** 评价应有人数 */
  cpycrs: number | null
  /** 评价实际人数 */
  cpscrs: number | null
  /** 评价平均分 */
  cppjf: number | null
  /** 评价有效人数 */
  cpyxrs: number | null
  /** 评价分数 */
  cppgdf: number | null
  /** 成绩提交按钮 */
  cjtjan: number
  /** 教学计划提交按钮 */
  skjhtjan: number
  /** 排课状态 */
  pkzt: number
  /** 课程标准附件 */
  kcbzfj: string
  /** 成绩模式锁定状态 */
  cjmdsdzt: number
  /** 数据分析 */
  sjfx: string | null
  /** 工作簿提交状态 */
  gzsctjbz: number
  /** 工作簿教研室审核 */
  gzscjyssh: number
  /** 工作簿院系审核 */
  gzscxbsh: number
  /** 是否排除考勤考试 */
  sfpczgjc: number
  /** 教学方式 */
  jxfs: string
  /** 教学方式名称 */
  jxfsmc: string
  /** 管理教学平台 */
  gljxpt: string
  /** 课程ID */
  course_id: number
  /** 班级ID */
  class_id: number
}

/**
 * 教学任务课程安排数据详情
 */
export interface TeachingScheduleArrangementData {
  /** 上课次数 */
  skcs: number
  /** 排课节次说明 */
  pkjcsm: string[]
  /** 是否禁用 */
  disabled: boolean
  /** 排课节次 */
  pkjc: string
  /** 排课节次值 */
  pkjgval: string
}

/**
 * 教学任务课程安排响应
 */
export interface TeachingScheduleArrangementResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: {
    /** 教学任务课程信息 */
    jxrw: TeachingTaskCourseInfo
    /** 开放时间 */
    kfsj: string
    /** 周数 */
    zs: number
    /** 学期信息 */
    xlxx: string
    /** 详细数据 */
    data: TeachingScheduleArrangementData
  }
}

/**
 * 课程节次信息
 */
export interface CourseSessionInfo {
  /** 节次值 */
  value: string
  /** 节次标签 */
  label: string
  /** 备注信息，用逗号分隔的数字字符串 */
  bz: string
}

/**
 * 教学节次信息
 */
export interface TeachingSessionInfo {
  /** 周次 */
  zc: number
  /** 课程名称 */
  kcmc: string
  /** 主讲教师姓名 */
  zdjsxm: string
  /** 周次显示文本 */
  zcshow: string
}

/**
 * 教学任务课程节次请求参数
 */
export interface TeachingTaskSessionRequest {
  /** 教学任务ID */
  jxrwid: string
  /** 步骤 */
  step: number
}

/**
 * 教学任务课程节次响应数据
 */
export interface TeachingTaskSessionResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: {
    /** 教学节次列表 */
    jxjc: TeachingSessionInfo[]
    /** 节次信息列表 */
    jcxx: CourseSessionInfo[]
  }
}

/**
 * 教学日程安排请求参数
 */
export interface TeachingScheduleRequest {
  /** 教学任务ID */
  jxrwid: string
  /** 步骤 */
  step: number
}

/**
 * 教学日程调课信息
 */
export interface TeachingScheduleAdjustment {
  /** 调课日期 */
  tkrq: string
  /** 调换日期，如果为"0000-00-00"表示没有调换 */
  dhrq: string
  /** 调课备注 */
  remark: string
}

/**
 * 教学日程安排响应数据
 */
export interface TeachingScheduleResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: {
    /** 教学周数 */
    jsz: number
    /** 学期开始时间戳 */
    xqy: number
    /** 开学时间 */
    kxsj: string
    /** 休离安排，键为日期，值为调课信息数组 */
    xlap: Record<string, TeachingScheduleAdjustment[]>
  }
}

/**
 * 教学安排检查请求参数
 */
export interface TeachingScheduleCheckRequest {
  /** 教学任务ID */
  jxrwid: string
  /** 步骤 */
  step: number
  /** 教学安排数据，格式为：周次_日期_星期几_节次_类型 */
  skap: string[]
  /** 教学周次标记，1表示上课，0表示不上课 */
  jxzc: string
}

/**
 * 教学安排检查结果项
 */
export interface TeachingScheduleCheckItem {
  /** 序号 */
  xh: number
  /** 日期 */
  rq: string
  /** 周次 */
  zc: string
  /** 星期几 */
  xq: string
  /** 节次 */
  jc: string
  /** 结果信息，HTML格式 */
  result: string
  /** 错误标志，1表示有错误 */
  error: number
}

/**
 * 清空场地请求参数
 */
export interface ClearVenueRequest {
  /** 教学任务ID */
  jxrwid: number
  /** 授课计划ID */
  skjhid: number
}

/**
 * 教学安排检查响应
 */
export interface TeachingScheduleCheckResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: TeachingScheduleCheckItem[]
}

/**
 * 教学任务安排列表请求参数
 */
export interface TeachingScheduleArrangementListRequest {
  /** 教学任务ID */
  jxrwid: string
}

/**
 * 教学任务信息
 */
export interface TeachingTaskInfo {
  /** 任务ID */
  id: number
  /** 计划ID */
  ssjh: number
  /** 选课ID */
  ssxk: number | null
  /** 合班ID */
  sshb: number | null
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 任务类型 */
  rwlx: string
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 课程性质 */
  kcxz: string
  /** 是否主课程 */
  zgkc: number
  /** 考核方式 */
  khfs: string
  /** 课程总学时 */
  xqzxs: string
  /** 教学学时 */
  jkxs: string
  /** 实验学时 */
  syxs: string
  /** 上机学时 */
  sjxs: string
  /** 虚拟学时 */
  sxxs: string
  /** 周学时 */
  zxs: string
  /** 课程信息周学时 */
  zxspkxx: string
  /** 周数 */
  zs: number
  /** 课程信息周数 */
  zspkxx: number
  /** 学分 */
  kcxf: number
  /** 学校代码 */
  ssxy: string
  /** 院系代码 */
  ssxb: string
  /** 院系名称 */
  ssxbmc: string
  /** 教研室代码 */
  ssjys: string
  /** 教研室名称 */
  ssjysmc: string
  /** 备注 */
  remark: string | null
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 主讲教师代码 */
  zdjs: string
  /** 主讲教师姓名 */
  zdjsxm: string
  /** 其他教师姓名 */
  qtfdjsxm: string
  /** 其他教师代码 */
  qtfdjs: string
  /** 班级代码 */
  ssbj: string
  /** 班级名称 */
  bjmc: string
  /** 任务类型状态 */
  rwlxzt: string
  /** 任务执行状态 */
  zxzt: number
  /** 班级代表 */
  kdb: string
  /** 班级代表姓名 */
  kdbxm: string
  /** 班级代表是否开启签到 */
  kdbfzkqkf: number
  /** 是否考核 */
  sfkh: number
  /** 成绩平台类型 */
  cjptlx: string
  /** 成绩提交标志 */
  cjtjbz: number
  /** 教学要求标志 */
  jcxybz: number
  /** 教学要求说明 */
  jcxysm: string
  /** 操作人编号 */
  oprybh: string
  /** 上课场地代码 */
  skcddm: string | null
  /** 上课场地名称 */
  skcdmc: string | null
  /** 开始周 */
  ksz: number
  /** 结束周 */
  jsz: number
  /** 排课信息 */
  pkxx: string
  /** 教学周次 */
  jxzc: string
  /** 排课周次 */
  pkcz: string
  /** 必备知识技能 */
  bbzsjn: string
  /** 核心课程通过知识 */
  hxkctgzs: string
  /** 核心课程通过技能 */
  hxkctgjn: string
  /** 课程教学大纲信息 */
  kcjxdgxx: string
  /** 课程教学知识目标 */
  kcjyzsmb: string
  /** 课程教学能力目标 */
  kcjynlmb: string
  /** 课程教学素质目标 */
  kcjyszmb: string
  /** 课程考核比分 */
  kckhbf: string
  /** 教师手册分析 */
  jdzjs: string
  /** 教师手册课时 */
  jdzjsks: string
  /** 工作量系数 */
  gzlxs: string
  /** 人日学时 */
  yrdxs: string
  /** 授课计划审核 */
  skjhsh: number
  /** 评价应有人数 */
  cpycrs: number | null
  /** 评价实际人数 */
  cpscrs: number | null
  /** 评价平均分 */
  cppjf: number | null
  /** 评价有效人数 */
  cpyxrs: number | null
  /** 评价评定分 */
  cppgdf: number | null
  /** 成绩提交按钮 */
  cjtjan: number
  /** 授课计划提交按钮 */
  skjhtjan: number
  /** 排课状态 */
  pkzt: number
  /** 课程标准附件 */
  kcbzfj: string
  /** 成绩模式锁定状态 */
  cjmdsdzt: number
  /** 数据分析 */
  sjfx: string | null
  /** 工作簿提交标志 */
  gzsctjbz: number
  /** 工作簿教研室审核 */
  gzscjyssh: number
  /** 工作簿学部审核 */
  gzscxbsh: number
  /** 是否排除考勤考试 */
  sfpczgjc: number
  /** 教学方式 */
  jxfs: string
  /** 教学方式名称 */
  jxfsmc: string
  /** 管理教学平台 */
  gljxpt: string
  /** 课程ID */
  course_id: number
  /** 班级ID */
  class_id: number
  /** 关联教学平台课程名称 */
  gljxptkcmc: string
  /** 课程通过人数 */
  kctgrs: number
}

/**
 * 教师选项
 */
export interface TeacherOption {
  /** 教师编号 */
  value: string
  /** 教师姓名 */
  label: string
}

/**
 * 授课方式选项
 */
export interface TeachingMethodOption {
  /** 授课方式编码 */
  value: string
  /** 授课方式名称 */
  label: string
}

/**
 * 节次选项
 */
export interface SessionOption {
  /** 节次值 */
  value: string
  /** 节次名称 */
  label: string
}

/**
 * 学生信息
 */
export interface StudentInfo {
  /** 学生ID */
  id: number
  /** 座位号 */
  zwh: number
  /** 学生学号 */
  xsxh: string
  /** 学生姓名 */
  xm: string
  /** 所属班级名称 */
  ssbjmc: string
  /** 性别：1-男，2-女 */
  xb: number
  /** 出生年月 */
  csny: string
  /** 民族代码 */
  mzdm: string
  /** 政治面貌代码 */
  zzmmdm: string
  /** 移动电话 */
  yddh: string
  /** 电子信箱 */
  dzxx: string
  /** 学生类别 */
  xslb: string
  /** 学生状态 */
  xszt: string
  /** 学籍状态 */
  xjzt: string
}

/**
 * 学生列表查询参数
 */
export interface StudentListQuery {
  /** 页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 学生学号 */
  xsxh?: string
  /** 学生姓名 */
  xm?: string
  /** 座位号 */
  zwh?: string
  /** 移动电话 */
  yddh?: string
}

/**
 * 学生列表响应数据
 */
export interface StudentListResponse {
  /** 学生列表 */
  items: StudentInfo[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
  /** 教学任务信息 */
  jxrwxx: TeachingTaskItem
}

/**
 * 学生分组选项
 */
export interface StudentGroupOption {
  /** 分组ID */
  value: number
  /** 分组名称 */
  label: string
}

/**
 * 作业类型选项
 */
export interface AssignmentTypeOption {
  /** 作业类型编码 */
  value: string
  /** 作业类型名称 */
  label: string
}

/**
 * 场地类型选项
 */
export interface SiteTypeOption {
  /** 场地类型编码 */
  value: string
  /** 场地类型名称 */
  label: string
}

/**
 * 授课计划项
 */
export interface TeachingScheduleItem {
  /** 计划ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
  /** 周次 */
  zc: number
  /** 上课日期 */
  skrq: string
  /** 上课开始时间 */
  skkssj: string
  /** 上课结束时间 */
  skjssj: string
  /** 星期几 */
  xqs: number
  /** 节次 */
  jc: string
  /** 节次显示 */
  jcshow: string
  /** 授课内容 */
  sknl: string
  /** 授课方式 */
  skfs: string
  /** 授课方式名称 */
  skfsmc: string | null
  /** 作业题数 */
  zyts: number
  /** 作业评估方式 */
  zypgfs: string
  /** 作业评估方式名称 */
  zypgfsmc: string | null
  /** 负责人所在组 */
  fzrszs: string
  /** 使用仪器设备数量 */
  syyqsbsl: string
  /** 上课场地类型 */
  skcdlx: string
  /** 上课场地代码 */
  skcddm: string | null
  /** 上课场地名称 */
  skcdmc: string | null
  /** 上课教师 */
  skjs: string
  /** 上课教师姓名 */
  skjsxm: string
  /** 上课班级 */
  skbj: string
  /** 上课班级名称 */
  skbjmc: string
  /** 实验人数 */
  syrs: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作人编号 */
  oprybh: string
  /** 课时 */
  ks: number
  /** 性质 */
  xz: number
  /** 关联ID */
  glid: number
  /** 授课计划执行状态 */
  skjhzxzt: number
  /** 授课计划工作量录入状态 */
  skjhgzlrdzt: number
  /** 作业内容 */
  zynr: string | null
  /** 授课计划教师确认状态 */
  skjhjsqrzt: number
  /** 授课计划教师确认时间 */
  skjhjsqrsj: string
  /** 授课计划教师确认人编号 */
  skjhjsqrrybh: string | null
  /** 授课计划教师确认人姓名 */
  skjhjsqrryxm: string | null
  /** 授课计划学生确认状态 */
  skjhxsqrzt: number
  /** 授课计划学生确认时间 */
  skjhxsqrsj: string
  /** 授课计划学生确认学生学号 */
  skjhxsqrxsxh: string | null
  /** 授课计划学生确认学生姓名 */
  skjhxsqrxsxm: string | null
  /** 场地申报状态 */
  cdsbzt: number
  /** 场地申报使用情况 */
  cdsbsyqk: string
  /** 审批状态 */
  spzt: number
  /** 网络教学关联ID */
  wljxglid: number
  /** 学生分组ID */
  xsfzid: number
  /** 学生分组名称 */
  xsfzmc: string
  /** 附件列表 */
  fjlb: string
  /** 考勤照片 */
  kqzp: string
  /** 调停补课申请状态 */
  ttbksqzt: number
  /** 场所确认审核状态 */
  csqrshzt: number
  /** 作业附件列表 */
  zyfjlb: string | null
  /** 直播平台代码 */
  zbptdm: string | null
  /** 直播平台名称 */
  zbptmc: string | null
  /** 直播平台内容 */
  zbptnr: string | null
  /** 教学平台代码 */
  jxptdm: string | null
  /** 教学平台名称 */
  jxptmc: string | null
  /** 教学平台内容 */
  jxptnr: string | null
  /** 学生上课审核状态 */
  xsskshzt: number
  /** 唯一标识 */
  key: number
  /** 序号 */
  xh?: number
  /** 场地按钮状态 */
  roomBtn?: boolean
}

/**
 * 教学任务安排列表响应数据
 */
export interface TeachingScheduleArrangementListResponse {
  /** 教学任务信息 */
  jxrw: TeachingTaskInfo
  /** 是否显示按钮 */
  showBtn: boolean
  /** 授课教师列表 */
  skjs: TeacherOption[]
  /** 辅导教师列表 */
  fdjs: TeacherOption[]
  /** 授课方式列表 */
  skfs: TeachingMethodOption[]
  /** 节次列表 */
  jcList: SessionOption[]
  /** 授课计划列表 */
  skjhList: TeachingScheduleItem[]
  /** 学生分组列表 */
  xsfz: StudentGroupOption[]
  /** 作业代码列表 */
  zydm: AssignmentTypeOption[]
  /** 分组信息 */
  fzxx: any[]
  /** 场地类型列表 */
  cdlx: SiteTypeOption[]
  /** 课程列表 */
  courseList: any[]
  /** 授课计划列表(带序号) */
  list: TeachingScheduleItem[]
}

/**
 * 教学计划表请求参数
 */
export interface TeachingPlanTableRequest {
  /**
   * 教学任务ID
   */
  jxrwid: string
}

/**
 * 教学计划表响应数据
 */
export interface TeachingPlanTableResponse {
  /**
   * 响应代码，1表示成功
   */
  code: number
  /**
   * 响应消息
   */
  msg: string
  /**
   * 时间戳
   */
  time: number
  /**
   * 响应数据
   */
  data: {
    /**
     * 授课计划提交状态，1表示已提交
     */
    skjhtj: number
    /**
     * 授课计划HTML内容
     */
    content: string
    /**
     * 授课计划ID，用于审批流程
     */
    shid: number
  }
}

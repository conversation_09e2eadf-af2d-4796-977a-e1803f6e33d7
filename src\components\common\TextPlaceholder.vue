<template>
  <view class="form-input p-[16rpx] bg-gray-50 rounded-lg">
    <text class="text-sm" :class="textClass">
      {{ displayText }}
    </text>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

// Props定义
interface Props {
  /** 显示的文本值 */
  value?: string
  /** 占位文本 */
  placeholder?: string
  /** 是否显示加载状态 */
  loading?: boolean
  /** 加载时的文本 */
  loadingText?: string
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  placeholder: '暂无数据',
  loading: false,
  loadingText: '加载中...',
})

// 计算显示的文本
const displayText = computed(() => {
  if (props.loading) {
    return props.loadingText
  }
  return props.value || props.placeholder
})

// 计算文本样式类
const textClass = computed(() => {
  return {
    'text-gray-400': !props.value || props.loading,
  }
})
</script>

<style lang="scss" scoped>
.form-input {
  // 统一的表单输入框样式
}
</style>

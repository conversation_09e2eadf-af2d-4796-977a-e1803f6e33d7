<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '请假申请',
  },
}
</route>

<template>
  <view class="apply-container">
    <view class="page-content">
      <!-- 请假人信息 -->
      <view class="form-card">
        <view class="form-title">请假人</view>
        <view class="form-content">
          <view class="form-item">
            <view class="readonly-value">
              {{ studentInfo.studentName }}（{{ studentInfo.studentCode }}）
            </view>
          </view>
        </view>
      </view>

      <!-- 请假类型(学习请假、晚寝请假、全部请假) -->
      <view class="form-card">
        <view class="form-title">请假类型</view>
        <view class="type-grid">
          <view
            v-for="(item, index) in leaveTypes"
            :key="index"
            class="type-item"
            :class="{ active: leaveType === item.value }"
            @click="leaveType = item.value"
          >
            <view :class="[item.icon, 'type-icon']"></view>
            <text class="type-text">{{ item.label }}</text>
          </view>
        </view>
      </view>

      <!-- 请假类别(事假、病假等) -->
      <view class="form-card">
        <view class="form-title">请假类别</view>
        <view class="type-grid">
          <view
            v-for="(item, index) in leaveCategories"
            :key="index"
            class="type-item"
            :class="{ active: leaveCategory === item.value }"
            @click="leaveCategory = item.value"
          >
            <view :class="[item.icon, 'type-icon']"></view>
            <text class="type-text">{{ item.label }}</text>
          </view>
        </view>
      </view>

      <!-- 是否出校 -->
      <view class="form-card">
        <view class="form-title">是否出校</view>
        <view class="campus-options">
          <view
            class="campus-option"
            :class="{ active: isApplyForExitCampus === 0 }"
            @click="isApplyForExitCampus = 0"
          >
            <view class="i-carbon-home type-icon"></view>
            <text>不出校</text>
          </view>
          <view
            class="campus-option"
            :class="{ active: isApplyForExitCampus === 1 }"
            @click="isApplyForExitCampus = 1"
          >
            <view class="i-carbon-logout type-icon"></view>
            <text>出校</text>
          </view>
        </view>

        <!-- 出校详细信息 -->
        <view v-if="isApplyForExitCampus === 1" class="exit-campus-details">
          <FormItem margin-bottom="medium">
            <FormLabel text="紧急联系人" :required="true" />
            <wd-input v-model="emergencyContact" placeholder="请输入紧急联系人姓名" clearable />
          </FormItem>

          <FormItem margin-bottom="medium">
            <FormLabel text="紧急联系人电话" :required="true" />
            <wd-input
              v-model="emergencyContactPhone"
              placeholder="请输入紧急联系人电话"
              type="number"
              clearable
            />
          </FormItem>

          <FormItem margin-bottom="medium">
            <FormLabel text="本人电话" :required="true" />
            <wd-input
              v-model="personalPhone"
              placeholder="请输入本人电话"
              type="number"
              clearable
            />
          </FormItem>

          <FormItem margin-bottom="none">
            <FormLabel text="出校详细目的地" :required="true" />
            <wd-input v-model="detailedDestination" placeholder="请输入出校详细目的地" clearable />
          </FormItem>
        </view>
      </view>

      <!-- 请假时间 -->
      <view class="form-card">
        <view class="form-title">请假时间</view>
        <view class="form-content">
          <view class="time-selector">
            <view class="time-selector-item">
              <text class="item-label">
                开始时间
                <text class="text-required">*</text>
              </text>
              <wd-calendar type="datetime" v-model="startTime" @confirm="handleTimeChange" />
            </view>
            <view class="time-selector-item">
              <text class="item-label">
                结束时间
                <text class="text-required">*</text>
              </text>
              <wd-calendar type="datetime" v-model="endTime" @confirm="handleTimeChange" />
            </view>
          </view>
          <view class="time-info">
            <view class="form-item">
              <text class="item-label">请假天数</text>
              <view class="readonly-value">{{ leaveDays }}天</view>
            </view>
            <view class="form-item">
              <text class="item-label">请假时长</text>
              <view class="readonly-value">{{ leaveHours }}小时</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 请假附件 -->
      <view class="form-card">
        <view class="form-title">
          请假附件
          <text class="text-required">*</text>
        </view>
        <view class="form-content">
          <view class="upload-section">
            <view class="upload-area">
              <view
                class="upload-item add-btn"
                :class="{ disabled: uploadLoading }"
                @click="handleUpload"
              >
                <template v-if="uploadLoading">
                  <wd-loading size="36rpx" color="#3a8eff" />
                  <text class="upload-text">上传中...</text>
                </template>
                <template v-else>
                  <view class="i-carbon-add type-icon"></view>
                  <text class="upload-text">添加图片</text>
                </template>
              </view>
              <view v-for="(img, index) in uploadedImages" :key="index" class="upload-item">
                <image :src="img.url" mode="aspectFill" />
                <view class="delete-icon" @click="removeImage(index)">
                  <view class="i-carbon-close type-icon-small"></view>
                </view>
              </view>
            </view>
            <text class="upload-tip">支持jpg、png格式，单个文件不超过5MB</text>
          </view>
        </view>
      </view>

      <!-- 请假事由 -->
      <view class="form-card">
        <view class="form-title">
          请假事由
          <text class="text-required">*</text>
        </view>
        <view class="form-content">
          <wd-textarea
            v-model="reason"
            placeholder="请详细描述请假原因..."
            :maxlength="200"
            :rows="4"
            show-word-limit
          />
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <wd-button
          type="primary"
          block
          :loading="isSubmitting"
          :disabled="isSubmitting || uploadLoading"
          @click="submitApplication"
        >
          {{ isSubmitting ? '提交中...' : '提交申请' }}
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue'
import useUpload from '@/hooks/useUpload'
import type { LeaveApplicationForm } from '@/types/leave'
import type { DictData } from '@/types/system'
import type { StudentInfo } from '@/types/student'
import { submitLeaveApplication } from '@/service/leave'
import { loadDictData } from '@/utils/dict'
import { getStudentInfo } from '@/service/student'
import { useUserStore } from '@/store/user'
import FormItem from '@/components/common/FormItem.vue'
import FormLabel from '@/components/common/FormLabel.vue'

// 用户存储
const userStore = useUserStore()

// 学生信息
const studentInfo = ref<Partial<StudentInfo>>({
  studentName: '',
  studentCode: '',
})

// 获取学生信息
const fetchStudentInfo = async () => {
  try {
    // 尝试从服务器获取学生信息
    const data = await getStudentInfo()
    studentInfo.value = data
  } catch (error) {
    console.error('获取学生信息失败:', error)
    // 如果获取失败，回退到用户存储中的基本信息
    studentInfo.value = {
      studentName: userStore.userInfo.realname || '',
      studentCode: userStore.userInfo.username || '',
    }
  }
}

// 请假类型选项
const leaveTypes = ref([
  { value: '0', label: '学习请假', icon: 'i-carbon-notebook' },
  { value: '1', label: '晚寝请假', icon: 'i-carbon-time' },
  { value: '2', label: '全部请假', icon: 'i-carbon-calendar' },
])
const leaveType = ref('')

// 请假类别选项（从字典获取）
const leaveCategories = ref<(DictData & { icon: string; value: string; label: string })[]>([])
const leaveCategory = ref('')

// 是否出校
const isApplyForExitCampus = ref(0)

// 出校相关信息
const emergencyContact = ref('') // 紧急联系人
const emergencyContactPhone = ref('') // 紧急联系人电话
const personalPhone = ref('') // 本人电话
const detailedDestination = ref('') // 出校详细目的地

// 请假时间
const startTime = ref<number>(0)
const endTime = ref<number>(0)

// 兼容旧代码，保留leaveTime计算属性
const leaveTime = computed(() => {
  if (!startTime.value || !endTime.value) return []
  return [startTime.value, endTime.value]
})

// 计算请假天数和时长
const leaveDays = computed(() => {
  if (!startTime.value || !endTime.value) return '0'

  const start = new Date(startTime.value)
  const end = new Date(endTime.value)

  // 确保结束时间不早于开始时间
  if (end < start) return '0'

  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  return diffDays.toString()
})

const leaveHours = computed(() => {
  if (!startTime.value || !endTime.value) return '0.0'

  const start = new Date(startTime.value)
  const end = new Date(endTime.value)

  // 确保结束时间不早于开始时间
  if (end < start) return '0.0'

  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffHours = (diffTime / (1000 * 60 * 60)).toFixed(1)

  return diffHours
})

// 请假事由
const reason = ref('')
const uploadedImages = ref<{ url: string; id?: number; file_url?: string }[]>([])

// 使用上传钩子
const {
  loading: uploadLoading,
  error: uploadError,
  data: uploadData,
  run: runUpload,
} = useUpload<string>()

// 监听上传结果
watch(uploadData, (newVal) => {
  if (newVal) {
    try {
      const result = newVal as any
      if (result.code === 1 && result.data.file) {
        const fileInfo = result.data.file
        uploadedImages.value.push({
          url: fileInfo.url,
          id: fileInfo.id,
          file_url: fileInfo.url,
        })
      } else {
        uni.showToast({
          title: '上传失败',
          icon: 'none',
        })
      }
    } catch (error) {
      console.error('解析上传结果失败:', error)
      uni.showToast({
        title: '上传失败',
        icon: 'none',
      })
    }
  }
})

// 获取请假类别数据
onMounted(() => {
  // 获取学生信息
  fetchStudentInfo()

  // 获取请假类型字典
  loadDictData(['DM_XSQJDM'])
    .then((dicts) => {
      const dictItems = dicts.DM_XSQJDM

      // 为每种请假类型分配对应的Carbon图标
      const iconMap: Record<string, string> = {
        '1': 'i-carbon-document', // 事假
        '2': 'i-carbon-face-dissatisfied', // 病假
        '6': 'i-carbon-notebook', // 公假
        '7': 'i-carbon-data-table', // 实习
        '8': 'i-carbon-group-presentation', // 集训
        '9': 'i-carbon-help-desk', // 其它
      }

      // 转换数据格式
      leaveCategories.value = dictItems.map((item) => ({
        ...item,
        value: item.dictValue,
        label: item.dictLabel,
        icon: iconMap[item.dictValue] || 'i-carbon-help-desk',
      }))

      // 设置默认选中的请假类型
      if (leaveCategories.value.length > 0) {
        leaveCategory.value = leaveCategories.value[0].value
      }
    })
    .catch((error) => {
      console.error('获取请假类别失败:', error)
      uni.showToast({
        title: '获取请假类别失败',
        icon: 'none',
      })
    })
})

// 处理上传错误
watch(uploadError, (newVal) => {
  if (newVal) {
    uni.showToast({
      title: '上传失败',
      icon: 'none',
    })
  }
})

// 监听是否出校变化，清空出校相关字段
watch(isApplyForExitCampus, (newValue) => {
  if (newValue === 0) {
    // 选择不出校时，清空出校相关信息
    emergencyContact.value = ''
    emergencyContactPhone.value = ''
    personalPhone.value = ''
    detailedDestination.value = ''
  }
})

// 处理日期时间选择确认
const handleTimeChange = () => {
  // 如果结束时间早于开始时间，自动调整结束时间为开始时间
  if (startTime.value && endTime.value && endTime.value < startTime.value) {
    uni.showToast({
      title: '结束时间不能早于开始时间',
      icon: 'none',
    })
    endTime.value = startTime.value
  }
}

// 处理图片上传
const handleUpload = () => {
  if (uploadLoading.value) return
  runUpload()
}

// 移除图片
const removeImage = (index: number) => {
  uploadedImages.value.splice(index, 1)
}

// 电话号码正则表达式验证函数
const validatePhoneNumber = (phone: string): boolean => {
  // 支持手机号码和固定电话号码
  // 手机号码：1开头，第二位为3-9，总共11位数字
  // 固定电话：区号(3-4位) + 号码(7-8位)，支持带横线分隔
  const mobileRegex = /^1[3-9]\d{9}$/
  const landlineRegex = /^(\d{3,4}-?)?\d{7,8}$/

  return mobileRegex.test(phone) || landlineRegex.test(phone)
}

// 验证表单
const validateForm = (): boolean => {
  // 验证请假类型(学习请假、晚寝请假、全部请假)
  if (!leaveType.value) {
    uni.showToast({
      title: '请选择请假类型',
      icon: 'none',
    })
    return false
  }

  // 验证请假类别(事假、病假等)
  if (!leaveCategory.value) {
    uni.showToast({
      title: '请选择请假类别',
      icon: 'none',
    })
    return false
  }

  if (!startTime.value) {
    uni.showToast({
      title: '请选择开始时间',
      icon: 'none',
    })
    return false
  }

  if (!endTime.value) {
    uni.showToast({
      title: '请选择结束时间',
      icon: 'none',
    })
    return false
  }

  if (endTime.value < startTime.value) {
    uni.showToast({
      title: '结束时间不能早于开始时间',
      icon: 'none',
    })
    return false
  }

  if (!reason.value.trim()) {
    uni.showToast({
      title: '请填写请假事由',
      icon: 'none',
    })
    return false
  }

  // 验证出校相关信息
  if (isApplyForExitCampus.value === 1) {
    if (!emergencyContact.value.trim()) {
      uni.showToast({
        title: '请填写紧急联系人',
        icon: 'none',
      })
      return false
    }

    if (!emergencyContactPhone.value.trim()) {
      uni.showToast({
        title: '请填写紧急联系人电话',
        icon: 'none',
      })
      return false
    }

    // 验证紧急联系人电话格式
    if (!validatePhoneNumber(emergencyContactPhone.value.trim())) {
      uni.showToast({
        title: '请输入正确的紧急联系人电话号码',
        icon: 'none',
      })
      return false
    }

    if (!personalPhone.value.trim()) {
      uni.showToast({
        title: '请填写本人电话',
        icon: 'none',
      })
      return false
    }

    // 验证本人电话格式
    if (!validatePhoneNumber(personalPhone.value.trim())) {
      uni.showToast({
        title: '请输入正确的本人电话号码',
        icon: 'none',
      })
      return false
    }

    if (!detailedDestination.value.trim()) {
      uni.showToast({
        title: '请填写出校详细目的地',
        icon: 'none',
      })
      return false
    }
  }

  return true
}

// 提交申请
const isSubmitting = ref(false)
const submitApplication = () => {
  if (isSubmitting.value) return
  if (uploadLoading.value) {
    uni.showToast({
      title: '图片上传中，请稍候',
      icon: 'none',
    })
    return
  }

  if (!validateForm()) return

  isSubmitting.value = true

  // 准备附件列表
  const fileList =
    uploadedImages.value.length > 0
      ? uploadedImages.value.map((img) => img.file_url || '').join('|')
      : ''

  // 格式化请假时间
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  // 准备请假表单数据
  const formData: LeaveApplicationForm = {
    // 使用API实际参数名
    qjlx: leaveCategory.value, // 请假类别(事假、病假等)
    wqsq: parseInt(leaveType.value), // 请假类型(学习请假、晚寝请假、全部请假)
    qjsy: reason.value, // 请假事由
    sfsqcxm: isApplyForExitCampus.value, // 是否申请出校
    leaveTime: [startTime.value, endTime.value].map((time) => formatTime(time)),
    fjlb: fileList,
    studentCode: studentInfo.value.studentCode || '',
    studentName: studentInfo.value.studentName || '',
    // 出校相关信息
    lxr: emergencyContact.value, // 紧急联系人
    lxrdh: emergencyContactPhone.value, // 紧急联系人电话
    brdh: personalPhone.value, // 本人电话
    cxmdd: detailedDestination.value, // 出校详细目的地
  }

  // 调用提交接口
  submitLeaveApplication(formData)
    .then(() => {
      uni.showToast({
        title: '提交成功',
        icon: 'success',
      })

      // 提交成功后延迟跳转到请假列表页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    })
    .catch((err) => {
      console.error('提交请假申请失败:', err)
    })
    .finally(() => {
      isSubmitting.value = false
    })
}
</script>

<style lang="scss">
.apply-container {
  min-height: 100vh;
  padding: 20rpx;
  background-color: #f7f8fa;
}

.page-content {
  padding-bottom: 40rpx;
}

.form-card {
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-title {
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.text-required {
  color: #ff4d4f;
}

.form-content {
  font-size: 28rpx;
}

.type-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 110rpx;
  padding: 12rpx;
  background-color: #f5f7fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  transition: all 0.3s;

  &.active {
    color: #3a8eff;
    background-color: #ecf5ff;
    border-color: #3a8eff;
  }
}

.type-icon {
  font-size: 40rpx;
  line-height: 1;
}

.type-icon-small {
  font-size: 32rpx;
  line-height: 1;
}

.type-text {
  max-width: 100%;
  margin-top: 8rpx;
  overflow: hidden;
  font-size: 24rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.campus-options {
  display: flex;
  gap: 20rpx;
}

.campus-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 120rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #f5f7fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  transition: all 0.3s;

  text {
    margin-top: 10rpx;
  }

  &.active {
    color: #3a8eff;
    background-color: #ecf5ff;
    border-color: #3a8eff;
  }
}

.form-item {
  margin-bottom: 24rpx;
}

.item-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #666;
}

.readonly-value {
  display: inline-flex;
  align-items: center;
  min-height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f5f7fa;
  border-radius: 8rpx;
}

.time-selector {
  margin-bottom: 20rpx;
}

.time-selector-item {
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.time-info {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;

  .form-item {
    flex: 1;
  }
}

.upload-section {
  margin-top: 24rpx;
}

.upload-area {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.upload-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin: 10rpx;
  overflow: hidden;
  border-radius: 8rpx;

  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &.add-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    border: 2rpx dashed #dcdfe6;
    transition: all 0.3s;

    &:active {
      background-color: #e9ecf2;
    }

    &.disabled {
      pointer-events: none;
      opacity: 0.6;
    }
  }
}

.upload-text {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999;
}

.exit-campus-details {
  padding-top: 32rpx;
  margin-top: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.delete-icon {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
}

.upload-tip {
  display: block;
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #999;
}

.submit-section {
  padding: 40rpx 30rpx;
  margin-top: 40rpx;
}
/* 预先定义所有Carbon图标类，确保UnoCSS能够识别 */
.i-carbon-notebook,
.i-carbon-time,
.i-carbon-calendar,
.i-carbon-document,
.i-carbon-face-dissatisfied .i-carbon-help-desk,
.i-carbon-data-table,
.i-carbon-group-presentation,
.i-carbon-home,
.i-carbon-logout,
.i-carbon-add,
.i-carbon-close {
  /* 这些类会被UnoCSS识别和处理 */
}
</style>

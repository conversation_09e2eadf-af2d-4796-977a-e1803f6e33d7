import type { PageInfo } from './teachingTask'

// 教师信息接口返回类型
export interface TeacherInfoResponse {
  code: number
  msg: string
  time: number
  data: TeacherInfo
}

// 教师信息类型
export interface TeacherInfo {
  id: number
  orderId: number
  teacherCode: string
  retirementNumber: string
  schoolCode: string
  organization: number
  deptCode: string
  deptName: string
  deptName2: string
  establishmentTypeCode: string
  establishmentTypeName: string
  categoryCode: string
  categoryName: string
  establishmentStatusCode: string
  establishmentStatusName: string
  name: string
  gender: string
  birthDate: string
  ethnicGroupCode: string
  ethnicGroupName: string
  politicalOutlookCode: string
  politicalOutlookName: string
  IDTypeCode: string
  IDNumber: string
  attendPartyDate: string
  educationalCode?: string
  educationalName?: string
  degreeCode?: string
  degreeName?: string
  marriageCode?: string
  marriageName?: string
  graduationSchool?: string
  graduationMajor?: string
  positionCategoryCode?: string
  positionCategoryName?: string
  jobTitleLevelCode?: string
  jobTitleLevelName?: string
  jobTitleCode?: string
  jobTitleName?: string
  positionName?: string
  familyPlanningCode?: string
  familyPlanningName?: string
  mobile?: string
  officePhone?: string
  email?: string
  remark?: string
  originLocation: string[]
  originCountyName: string
  homeAddress: string
  homePhone: string
  attendWorkDate: string
  employmentDate: string
  entryDate: string
  retirementDate: string
  resignationDate: string
  avatarUrl: string
  rybh: string
  zgxlbm: string
  zgxlmc: string
  zgxwbm: string
  zgxwmc: string
  isDualRole: string
  isDualQualification: string
  inStaff: string
  sourceName: string
  imei: string
}

// 非教学工作量查询参数
export interface NonWorkloadQuery {
  page: number
  pageSize: number
  sortBy: string
  sortOrder: 'asc' | 'desc'
  workDate?: string[]
  workEndDate?: string[]
  workload?: string
  content?: string
  auditStatus?: string
  semesters?: string[]
}

// 非教学工作量项
export interface NonWorkloadItem {
  id: number
  userCode: string
  name: string
  studyYear: string
  studyTerm: number
  workDate: string
  workEndDate: string
  workload: string
  content: string
  deltag: number
  auditStatus: string
  operatorCode: string
  create_time: string
  update_time: string
  remark: string
  attachmentList: string
  relatedActivityId: number
}

// 非教学工作量响应
export interface NonWorkloadResponse {
  items: NonWorkloadItem[]
  total: number
  query: Record<string, any>
}

// 教工通讯录查询参数
export interface TeacherContactQuery {
  page: number
  pageSize: number
  sortBy: string
  sortOrder: 'asc' | 'desc'
  teacherCode?: string
  name?: string
  gender?: '0' | '1' | '2' | ''
  mobile?: string
  officePhone?: string
  establishmentStatusName?: string
}

// 教工通讯录项
export interface TeacherContact {
  id: number
  teacherCode: string
  name: string
  gender: string
  mobile: string
  officePhone: string
  establishmentStatusName: string
  deptName: string
  positionName: string
  email: string
  pykssy?: string
}

// 教工通讯录响应
export interface TeacherContactResponse {
  items: TeacherContact[]
  total: number
  query: Record<string, any>
}

export interface SelectCourseInfoParams {
  /** 选课ID */
  selectCourseId: number
}

export interface SiteInfo {
  /** 星期 */
  week: string
  /** 节次 */
  section: string
  /** 节次键 */
  sectionKey: string
  /** 场地类型 */
  siteType: string
}

export interface TeachingMaterial {
  /** 教材ID */
  id: number
  /** 教材名称 */
  name: string
  /** 教材类型 */
  type: string
  /** 作者 */
  author: string
  /** 出版社 */
  press: string
  /** 版次 */
  edition: string
  /** 出版时间 */
  publishTime: string
  /** 状态 */
  status: number
}

export interface SelectCourseInfo {
  /** 选课ID */
  id: number
  /** 校区代码 */
  campusCode: string
  /** 校区名称 */
  campusName: string
  /** 起始年级 */
  startingGrade: string[]
  /** 起始班级部门代码 */
  startClassDeptCode: string[]
  /** 起始班级部门名称 */
  startClassDeptName: string
  /** 专业代码 */
  majorCode: string
  /** 专业名称 */
  majorName: string
  /** 班级 */
  class: string[]
  /** 班级名称 */
  className: string
  /** 最大人数 */
  maxCount: number
  /** 限制人数 */
  limitCount: number
  /** 课程标题 */
  courseTitle: string
  /** 课程类别 */
  courseCategory: string
  /** 课程类别名称 */
  courseCategoryName: string
  /** 类型 */
  type: string
  /** 教研室审核 */
  teachOfficeAudit: number
  /** 系部审核 */
  deptAudit: number
  /** 教务审批 */
  academicAffairsApproval: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 合并任务ID */
  mergeTaskId: number | null
  /** 操作员编号 */
  oprybh: string
  /** 已选人数 */
  selectedCount: number
  /** 添加权限 */
  addPermission: number
  /** 状态 */
  status: number
  /** 实习开始状态 */
  internshipStartStatus: number
  /** 实习状态 */
  internshipStatus: number
  /** 实习开始时间 */
  internshipStartTime: string
  /** 实习结束时间 */
  internshipEndTime: string
  /** 计划ID */
  planId: number | null
  /** 选课ID */
  selectCourseId: number
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: number
  /** 任务类型 */
  taskType: string
  /** 课程代码 */
  courseCode: string
  /** 课程名称 */
  courseName: string
  /** 课程类型 */
  courseType: string
  /** 是否主课程 */
  mainCourse: number
  /** 考核方式 */
  assessmentMethod: string
  /** 课程总课时 */
  courseTotalHours: string
  /** 教学课时 */
  teachingHours: string
  /** 实验课时 */
  experimentHours: string
  /** 计算机课时 */
  computerHours: string
  /** 虚拟课时 */
  virtualHours: string
  /** 周课时 */
  weekHours: string
  /** 课程信息周课时 */
  weekHoursCourseInfo: string
  /** 周数 */
  weeks: number
  /** 课程信息周数 */
  weeksCourseInfo: number
  /** 学分 */
  creditHour: number
  /** 学校代码 */
  schoolCode: string
  /** 系部代码 */
  deptCode: string
  /** 系部名称 */
  deptName: string
  /** 教研室代码 */
  teachOfficeCode: string
  /** 教研室名称 */
  teachOfficeName: string
  /** 主讲教师代码 */
  leaderTeacherCode: string
  /** 主讲教师名称 */
  leaderTeacherName: string
  /** 其他教师名称 */
  otherTeacherName: string
  /** 其他教师代码 */
  otherTeacherCode: string
  /** 班级代码 */
  classCode: string
  /** 任务类型状态 */
  taskTypeStatus: string
  /** 任务执行状态 */
  taskExecutionStatus: number
  /** 班级代表 */
  sectionRepresentative: string | null
  /** 班级代表名称 */
  sectionRepresentativeName: string
  /** 考勤开放 */
  checkingInOpen: number
  /** 是否考核 */
  isAssessment: string
  /** 考核类型 */
  assessmentType: string
  /** 提交状态 */
  submitStatus: number
  /** 教材使用 */
  textbookUse: number
  /** 操作员代码 */
  operatorCode: string
  /** 场地代码 */
  siteCode: string | null
  /** 场地名称 */
  siteName: string | null
  /** 开始周 */
  startWeek: number
  /** 结束周 */
  endWeek: number
  /** 教学信息 */
  teachingInfo: string
  /** 教学周 */
  teachingWeek: string | null
  /** 班级周 */
  classWeeks: string
  /** 学生知识 */
  studentKnowledge: string | null
  /** 后续课程知识 */
  followingCourseKnowledge: string | null
  /** 后续课程技能 */
  followingCourseSkill: string | null
  /** 教学大纲 */
  teachingOutline: string | null
  /** 知识目标 */
  knowledgeObjective: string | null
  /** 能力目标 */
  abilityObjective: string | null
  /** 素质目标 */
  qualityObjective: string
  /** 考核方式 */
  assessmentWay: string | null
  /** 配对教师 */
  pairTeacher: string
  /** 配对教师课时 */
  pairTeacherHours: string
  /** 工作量数 */
  workloadNum: string
  /** 确认课时 */
  affirmHours: string
  /** 教学计划审批 */
  teachingPlanApproval: number
  /** 评教应该数 */
  evaluationShouldNum: number | null
  /** 评教实际数 */
  evaluationActualNum: number | null
  /** 评教平均分 */
  evaluationAverageScore: number | null
  /** 评教有效数 */
  evaluationEffectiveNum: number | null
  /** 评教得分 */
  evaluationScore: number | null
  /** 是否提交按钮 */
  isSubmitButton: number
  /** 教学计划提交 */
  teachingPlanSubmit: number
  /** 教学计划状态 */
  teachingPlanStatus: number
  /** 课程标准附件 */
  courseStandardAttachment: number
  /** 是否课程评价锁定 */
  isCourseEvaluationLock: number
  /** 教师手动分析 */
  teacherManualAnalysis: string | null
  /** 作业本提交状态 */
  workbookSubmitStatus: number
  /** 作业本教研室审批 */
  workbookTeachOfficeApproval: number
  /** 作业本系部审批 */
  workbookDeptApproval: number
  /** 是否排除考勤考试 */
  isExcludeAttendanceExam: number
  /** 教学方法 */
  teachingMethod: string
  /** 教学方法名称 */
  teachingMethodName: string
  /** 关联教学平台 */
  gljxpt: string
  /** 课程ID */
  course_id: number
  /** 班级ID */
  class_id: number
  /** 教学材料列表 */
  teachingMaterialList: TeachingMaterial[]
  /** 场地信息列表 */
  siteInfoList: SiteInfo[]
  /** 学期 */
  semesters: string
}

// 教师工作量查询参数
export interface TeacherWorkloadQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 学期列表 */
  semesters: string[]
  /** 班级名称 */
  className?: string
  /** 课程名称 */
  courseName?: string
  /** 认定教师姓名 */
  identifyTeacherName?: string
  /** 认定系数 */
  identifyCoefficient?: string
  /** 认定工作量 */
  identifyWorkload?: string
  /** 追加工作量 */
  addWorkload?: string
  /** 备注 */
  remark?: string
  /** 最终工作量 */
  finalWorkload?: string
  /** 上课日期 */
  giveLessonsDate?: string
  /** 周次 */
  week?: string
  /** 节次 */
  sectionShow?: string
  /** 授课模式名称 */
  giveLessonsModeName?: string
  /** 授课内容 */
  giveLessonsContent?: string
  /** 场地名称 */
  siteName?: string
  /** 周期 */
  cycle?: string
  /** 排序字段 */
  sortBy?: string
  /** 排序方向 */
  sortOrder?: string
}

// 教师工作量项
export interface TeacherWorkloadItem {
  /** ID */
  id: number
  /** 教学任务ID */
  teachingTasksId: number
  /** 教学计划ID */
  teachingPlanId: number | null
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: number
  /** 周期 */
  cycle: number
  /** 认定教师编码 */
  identifyTeacherCode: string
  /** 认定教师姓名 */
  identifyTeacherName: string
  /** 认定系数 */
  identifyCoefficient: string
  /** 认定工作量 */
  identifyWorkload: string
  /** 追加工作量 */
  addWorkload: string
  /** 备注 */
  remark: string | null
  /** 最终工作量 */
  finalWorkload: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编码 */
  operatorCode: string
  /** 认定类型 */
  identifyType: number
  /** 授课日期 */
  giveLessonsDate: string | null
  /** 周次 */
  week: number | null
  /** 节次 */
  section: string | null
  /** 显示节次 */
  sectionShow: string | null
  /** 授课内容 */
  giveLessonsContent: string | null
  /** 授课模式 */
  giveLessonsMode: string | null
  /** 授课模式名称 */
  giveLessonsModeName: string | null
  /** 场地类型 */
  siteType: string | null
  /** 场地编码 */
  siteCode: string | null
  /** 场地名称 */
  siteName: string | null
  /** 教师编码 */
  teacherCode: string | null
  /** 教师姓名 */
  teacherName: string | null
  /** 班级ID */
  classId: string | null
  /** 课时 */
  classHour: number | null
  /** 质量 */
  quality: number | null
  /** 教学计划状态 */
  teachingPlanStatus: number | null
  /** 工作量状态 */
  workloadStatus: number | null
  /** 选课ID */
  selectCourseId: number | null
  /** 合并任务ID */
  mergeTaskId: number | null
  /** 课程编码 */
  courseCode: string
  /** 课程名称 */
  courseName: string
  /** 课程总学时 */
  courseTotalHours: string
  /** 周课时 */
  weekHours: string
  /** 周数 */
  weeks: number
  /** 学校编码 */
  schoolCode: string
  /** 系部编码 */
  deptCode: string
  /** 教研室编码 */
  teachOfficeCode: string
  /** 班级编码 */
  classCode: string
  /** 班级名称 */
  className: string
  /** 工作量数 */
  workloadNum: string
  /** 确认课时 */
  affirmHours: string
  /** 主讲教师名称 */
  leaderTeacherName: string
  /** 任务类型状态 */
  taskTypeStatus: string
}

// 教师工作量响应
export interface TeacherWorkloadResponse {
  /** 工作量列表 */
  items: TeacherWorkloadItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
  /** 周数 */
  week: number
}

// 教学日志查询参数
export interface TeachingLogQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 周期 */
  cycle?: string | number
  /** 班级名称 */
  className?: string
  /** 课程名称 */
  courseName?: string
  /** 周次 */
  week?: string
  /** 授课日期 */
  giveLessonsDate?: string
  /** 节次显示 */
  sectionShow?: string
  /** 场地名称 */
  siteName?: string
  /** 授课模式名称 */
  giveLessonsModeName?: string
  /** 授课内容 */
  giveLessonsContent?: string
  /** 学生确认状态 */
  studentConfirmStatus?: string
  /** 学生确认人 */
  studentConfirmName?: string
  /** 学生确认日期 */
  studentConfirmDate?: string
  /** 教师确认状态 */
  teacherConfirmStatus?: string
  /** 教师确认日期 */
  teacherConfirmDate?: string
  /** 教学计划状态 */
  teachingPlanStatus?: string
  /** 工作量状态 */
  workloadStatus?: string
  /** 学期列表 */
  semesters: string[]
}

// 教学日志项
export interface TeachingLogItem {
  /** ID */
  id: number
  /** 教学任务ID */
  teachingTasksId: number
  /** 周期 */
  cycle: number
  /** 授课日期 */
  giveLessonsDate: string
  /** 授课开始时间 */
  giveLessonsStartTime: string
  /** 授课结束时间 */
  giveLessonsEndTime: string
  /** 周次 */
  week: number
  /** 节次 */
  section: string
  /** 节次显示 */
  sectionShow: string
  /** 授课内容 */
  giveLessonsContent: string
  /** 授课模式 */
  giveLessonsMode: string
  /** 授课模式名称 */
  giveLessonsModeName: string
  /** 作业数量 */
  homeworkNum: number
  /** 作业检查模式 */
  homeworkCheckMode: string
  /** 作业检查模式名称 */
  homeworkCheckModeName: string
  /** 辅助上课人数 */
  fzrszs: string
  /** 实验仪器设备数量 */
  syyqsbsl: string
  /** 场地类型 */
  siteType: string
  /** 场地编码 */
  siteCode: string
  /** 场地名称 */
  siteName: string
  /** 教师编码 */
  teacherCode: string
  /** 教师姓名 */
  teacherName: string
  /** 班级ID */
  classId: string
  /** 班级名称 */
  className: string
  /** 实验人数 */
  syrs: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编码 */
  operatorCode: string
  /** 课时 */
  classHour: number
  /** 质量 */
  quality: number
  /** 关联ID */
  glid: number
  /** 教学计划状态 */
  teachingPlanStatus: string
  /** 工作量状态 */
  workloadStatus: string
  /** 作业内容 */
  homeworkContent: string
  /** 教师确认状态 */
  teacherConfirmStatus: string
  /** 教师确认日期 */
  teacherConfirmDate: string
  /** 教师确认编码 */
  teacherConfirmCode: string
  /** 教师确认姓名 */
  teacherConfirmName: string
  /** 学生确认状态 */
  studentConfirmStatus: string
  /** 学生确认日期 */
  studentConfirmDate: string
  /** 学生确认编码 */
  studentConfirmCode: string
  /** 学生确认姓名 */
  studentConfirmName: string
  /** 迟到上报状态 */
  cdsbzt: number
  /** 迟到上报事由情况 */
  cdsbsyqk: string
  /** 审批状态 */
  approvalStatus: number
  /** 网络教学关联ID */
  wljxglid: number
  /** 学生负责ID */
  xsfzid: number
  /** 学生负责名称 */
  xsfzmc: string
  /** 附件内容 */
  attachmentContent: string
  /** 考勤照片 */
  kqzp: string | null
  /** 调整补课申请状态 */
  ttbksqzt: number
  /** 加班审批结果 */
  overtimeApprovalResult: number
  /** 作业附件内容 */
  homeworkAttachmentContent: string
  /** 直播平台编码 */
  livePlatformCode: string
  /** 直播平台名称 */
  livePlatformName: string
  /** 直播平台内容 */
  livePlatformContent: string
  /** 教学平台编码 */
  teachingPlatformCode: string
  /** 教学平台名称 */
  teachingPlatformName: string
  /** 教学平台内容 */
  teachingPlatformContent: string
  /** 学生刷课审核状态 */
  xsskshzt: number
  /** 课程名称 */
  courseName: string
}

// 教学日志响应
export interface TeachingLogResponse {
  /** 教学日志列表 */
  items: TeachingLogItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
  /** 周数 */
  week: number
}

/**
 * 教师工作量统计响应接口
 */
export interface TeacherWorkloadStatisticsResponse {
  /**
   * 学期列表
   */
  semesters: SemesterWorkloadStats[]
  /**
   * 总计工作量
   */
  total: WorkloadStatsTotal
}

/**
 * 教学任务总分类型信息查询参数
 */
export interface ScoreTypeInfoQuery {
  /** 教学任务ID */
  jxrwid: string | number
  /** 类型 */
  type: string
}

/**
 * 分数选项接口
 */
export interface ScoreOption {
  /** 分数值 */
  value: string
  /** 标题 */
  title: string
  /** 显示标签 */
  label: string
}

/**
 * 分数时间范围接口
 */
export interface ScoreTimeRange {
  /** 开始时间 */
  start: string
  /** 结束时间 */
  end: string
}

/**
 * 教学任务总分类型信息响应数据
 */
export interface ScoreTypeInfoResponse {
  /** 类型编码 */
  type: string
  /** 类型名称 */
  name: string
  /** 分数选项列表 */
  options: ScoreOption[]
  /** 最大分数值 */
  max: number
  /** 时间范围 */
  time: ScoreTimeRange
  /** 是否已提交 0-未提交 1-已提交 */
  submit: number
  /** 是否已申请 0-未申请 1-已申请 */
  apply: number
  /** 是否允许 0-不允许 1-允许 */
  allow: number
}

/**
 * 学期工作量统计项
 */
export interface SemesterWorkloadStats {
  /**
   * 学期标签，如 "2023-2024-1"
   */
  label: string
  /**
   * 学期值，如 "2023-2024-1"
   */
  value: string
  /**
   * 学年制，如 "冬令制"、"夏令制" 或空字符串
   */
  seasonal: string
  /**
   * 是否当前学期
   */
  isCurrent: boolean
  /**
   * 该学期的工作量统计数据
   */
  statistics: {
    /**
     * 理论课时数
     */
    theoryHours: string | null
    /**
     * 实践课时数
     */
    practiceHours: number
    /**
     * 总课时数
     */
    totalHours: string | null
  }
}

/**
 * 总工作量统计
 */
export interface WorkloadStatsTotal {
  /**
   * 总理论课时数
   */
  theoryHours: number
  /**
   * 总实践课时数
   */
  practiceHours: number
  /**
   * 总课时数
   */
  totalHours: number
}

/**
 * 听课记录查询参数
 */
export interface AttendLectureQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 学期列表，格式为["学年|学期"] */
  semesters: string
  /** 系部名称 */
  deptName?: string
  /** 授课教师编码 */
  lectureTeacherCode?: string
  /** 授课教师姓名 */
  lectureTeacherName?: string
  /** 班级名称 */
  className?: string
  /** 课程名称 */
  courseName?: string
  /** 上课日期 */
  giveLessonsDate?: string
  /** 节次显示 */
  sectionShow?: string
  /** 教师姓名 */
  teacherName?: string
  /** 评价状态 */
  evaluationStatus?: string
  /** 评价分数 */
  evaluationScore?: string
  /** 审批状态 */
  approvalStatus?: string
}

/**
 * 听课记录项
 */
export interface AttendLectureItem {
  /** ID */
  id: number
  /** 授课类型 */
  lectureType: number
  /** 授课学年 */
  lectureYear: string
  /** 授课学期 */
  lectureTerm: string
  /** 教学计划ID */
  teachingPlanId: number
  /** 课程类型 */
  courseType: string
  /** 授课教师编码 */
  lectureTeacherCode: string
  /** 授课教师姓名 */
  lectureTeacherName: string
  /** 教学内容 */
  teachingContent: string
  /** 授课记录 */
  lectureRecord: string
  /** 教学进度和计划 */
  teachingProgressAndPlan: string
  /** 师生反馈 */
  teacherStudentFeedback: string
  /** 讲师意见 */
  lecturerOpinion: string
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编码 */
  operatorCode: string
  /** 审核状态 */
  auditStatus: string
  /** 附件列表 */
  attachmentList: string
  /** 评价状态 */
  evaluationStatus: string
  /** 听课评价 */
  lectureEvaluation: string
  /** 评价分数 */
  evaluationScore: number
  /** 评价时间 */
  evaluationTime: string
  /** 学习学年 */
  studyYear: string
  /** 学习学期 */
  studyTerm: number
  /** 选课ID */
  selectCourseId: number | null
  /** 课程编码 */
  courseCode: string
  /** 课程名称 */
  courseName: string
  /** 课程总学时 */
  courseTotalHours: string
  /** 周课时 */
  weekHours: string
  /** 周数 */
  weeks: number
  /** 学校编码 */
  schoolCode: string
  /** 系部编码 */
  deptCode: string
  /** 系部名称 */
  deptName: string
  /** 班级编码 */
  classCode: string
  /** 班级名称 */
  className: string
  /** 工作量数 */
  workloadNum: string
  /** 确认课时 */
  affirmHours: string
  /** 主讲教师姓名 */
  leaderTeacherName: string
  /** 退休编号 */
  retirementNumber: string
  /** 组织 */
  organization: number
  /** 系部名称2 */
  deptName2: string | null
  /** 编制类型编码 */
  establishmentTypeCode: string
  /** 类别编码 */
  categoryCode: string
  /** 编制状态编码 */
  establishmentStatusCode: string
  /** 编制类型名称 */
  establishmentTypeName: string
  /** 类别名称 */
  categoryName: string
  /** 编制状态名称 */
  establishmentStatusName: string
  /** 教研室编码 */
  teachOfficeCode: string
  /** 教研室名称 */
  teachOfficeName: string
  /** 就业日期 */
  employmentDate: string
  /** 入职日期 */
  entryDate: string | null
  /** 来源 */
  source: string | null
  /** 来源名称 */
  sourceName: string | null
  /** 在职人员 */
  inStaff: string | null
  /** 是否双师型 */
  isDualQualification: string | null
  /** 退休日期 */
  retirementDate: string | null
  /** 离职日期 */
  resignationDate: string | null
  /** 学校ID */
  schoolId: string
  /** 名称 */
  name: string
  /** 编码 */
  code: string
  /** 是否教学部门 */
  isTeachingDept: number
  /** 父级ID */
  parentId: number
  /** 教学任务ID */
  teachingTasksId: number
  /** 周期 */
  cycle: number
  /** 上课日期 */
  giveLessonsDate: string
  /** 周次 */
  week: number
  /** 节次 */
  section: string
  /** 节次显示 */
  sectionShow: string
  /** 上课内容 */
  giveLessonsContent: string
  /** 上课模式 */
  giveLessonsMode: string
  /** 上课模式名称 */
  giveLessonsModeName: string
  /** 场地类型 */
  siteType: string
  /** 场地编码 */
  siteCode: string
  /** 场地名称 */
  siteName: string
  /** 教师编码 */
  teacherCode: string
  /** 教师姓名 */
  teacherName: string
  /** 班级ID */
  classId: string
  /** 作业数量 */
  homeworkNum: number
  /** 作业检查模式 */
  homeworkCheckMode: string
  /** 作业检查模式名称 */
  homeworkCheckModeName: string
  /** 作业内容 */
  homeworkContent: string
  /** 课时 */
  classHour: number
  /** 质量 */
  quality: number
  /** 教学计划状态 */
  teachingPlanStatus: number
  /** 工作量状态 */
  workloadStatus: number
  /** 教师确认状态 */
  teacherConfirmStatus: number
  /** 教师确认日期 */
  teacherConfirmDate: string
  /** 教师确认编码 */
  teacherConfirmCode: string
  /** 教师确认姓名 */
  teacherConfirmName: string
  /** 学生确认状态 */
  studentConfirmStatus: number
  /** 学生确认日期 */
  studentConfirmDate: string
  /** 学生确认编码 */
  studentConfirmCode: string
  /** 学生确认姓名 */
  studentConfirmName: string
  /** 审批状态 */
  approvalStatus: string
  /** 评价状态名称 */
  evaluationStatusName?: string
  /** 审核状态值 */
  shzt?: number
  /** 审核状态名称 */
  auditStatusName?: string
}

/**
 * 听课记录响应
 */
export interface AttendLectureResponse {
  /** 听课记录列表 */
  items: AttendLectureItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
}

/**
 * 听课评估项目查询参数
 */
export interface EvaluationProjectQuery {
  /** 学期，格式为"学年|学期"，例如"2024-2025|2" */
  semesters: string
}

/**
 * 听课评估项目项
 */
export interface EvaluationProjectItem {
  /** ID */
  id: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 适用对象 */
  sydx: string
  /** 适用对象名称 */
  sydxmc: string
  /** 适用教学任务类别 */
  syjxrwlb: string
  /** 适用教学任务类别名称 */
  syjxrwlbmc: string
  /** 题目类型 */
  tmlx: string
  /** 题目类型名称 */
  tmlxmc: string
  /** 测评题目 */
  cptm: string
  /** 测评选项，格式为"选项1|分值1&&选项2|分值2..." */
  cpxx: string
  /** 测评题目说明 */
  cptmsm: string
  /** 排序号 */
  pxh: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
}

/**
 * 听课评估项目响应数据
 */
export interface EvaluationProjectResponse {
  /** 评估项目列表 */
  items: EvaluationProjectItem[]
  /** 消息 */
  message: string
}

/**
 * 教师课程表查询参数
 */
export interface TeacherScheduleQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 学期，格式为"学年|学期"，例如"2024-2025|2" */
  semesters: string
  /** 周次 */
  zc?: number | string
  /** 班级名称 */
  skbjmc?: string
  /** 课程名称 */
  kcmc?: string
  /** 星期 */
  xqs?: string | number
  /** 节次显示 */
  jcshow?: string
  /** 上课场地名称 */
  skcdmc?: string
  /** 上课方式代码 */
  skfsdm?: string
  /** 上课内容 */
  sknl?: string
  /** 学生确认状态 */
  skjhxsqrzt?: string | number
  /** 学生确认姓名 */
  skjhxsqrxsxm?: string
  /** 教师确认状态 */
  skjhjsqrzt?: string | number
  /** 教学中心状态 */
  skjhzxzt?: string | number
  /** 工作量录入状态 */
  skjhgzlrdzt?: string | number
}

/**
 * 教师课程表项
 */
export interface TeacherScheduleItem {
  /** ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
  /** 周次 */
  zc: number
  /** 上课日期 */
  skrq: string
  /** 上课开始时间 */
  skkssj: string
  /** 上课结束时间 */
  skjssj: string
  /** 星期 */
  xqs: number
  /** 节次 */
  jc: string
  /** 显示节次 */
  jcshow: string
  /** 上课内容 */
  sknl: string
  /** 上课方式 */
  skfs: string
  /** 上课方式名称 */
  skfsmc: string
  /** 作业提示 */
  zyts: number
  /** 作业评估方式 */
  zypgfs: string
  /** 作业评估方式名称 */
  zypgfsmc: string
  /** 辅助人数 */
  fzrszs: string
  /** 实验仪器设备数量 */
  syyqsbsl: string
  /** 上课场地类型 */
  skcdlx: string
  /** 上课场地代码 */
  skcddm: string
  /** 上课场地名称 */
  skcdmc: string
  /** 上课教师 */
  skjs: string
  /** 上课教师姓名 */
  skjsxm: string
  /** 上课班级 */
  skbj: string
  /** 上课班级名称 */
  skbjmc: string
  /** 实验人数 */
  syrs: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 课时 */
  ks: number
  /** 学制 */
  xz: number
  /** 关联ID */
  glid: number
  /** 教学计划中心状态 */
  skjhzxzt: number
  /** 工作量录入状态 */
  skjhgzlrdzt: number
  /** 作业内容 */
  zynr: string
  /** 教师确认状态 */
  skjhjsqrzt: number
  /** 教师确认时间 */
  skjhjsqrsj: string
  /** 教师确认人编号 */
  skjhjsqrrybh: string
  /** 教师确认人姓名 */
  skjhjsqrryxm: string
  /** 学生确认状态 */
  skjhxsqrzt: number
  /** 学生确认时间 */
  skjhxsqrsj: string
  /** 学生确认学号 */
  skjhxsqrxsxh: string
  /** 学生确认姓名 */
  skjhxsqrxsxm: string
  /** 迟到上报状态 */
  cdsbzt: number
  /** 迟到上报事由情况 */
  cdsbsyqk: string
  /** 审批状态 */
  spzt: number
  /** 网络教学关联ID */
  wljxglid: number
  /** 学生负责ID */
  xsfzid: number
  /** 学生负责名称 */
  xsfzmc: string
  /** 附件类别 */
  fjlb: string
  /** 考勤照片 */
  kqzp: string
  /** 调整补课申请状态 */
  ttbksqzt: number
  /** 迟到确认审核状态 */
  csqrshzt: number
  /** 作业附件类别 */
  zyfjlb: string
  /** 直播平台代码 */
  zbptdm: string
  /** 直播平台名称 */
  zbptmc: string
  /** 直播平台内容 */
  zbptnr: string
  /** 教学平台代码 */
  jxptdm: string
  /** 教学平台名称 */
  jxptmc: string
  /** 教学平台内容 */
  jxptnr: string
  /** 学生刷课审核状态 */
  xsskshzt: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 主讲教师 */
  zdjs: string
  /** 主讲教师姓名 */
  zdjsxm: string
}

/**
 * 教师课程表响应
 */
export interface TeacherScheduleResponse {
  /** 课程表项列表 */
  items: TeacherScheduleItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
}

/**
 * 教师考勤列表查询参数
 */
export interface TeacherAttendListQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 授课计划ID */
  skjhid: number
  /** 教学任务ID */
  jxrwid: number
}

/**
 * 教师考勤学生项
 */
export interface TeacherAttendItem {
  /** 考勤数据类型 */
  kqsjlx: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 教学任务ID */
  jxrwid: number
  /** 课程名称 */
  kcmc: string
  /** 周次 */
  zc: number
  /** 上课日期 */
  skrq: string
  /** 考勤开始时间 */
  kqkssj: string
  /** 考勤结束时间 */
  kqjssj: string
  /** 节次显示 */
  jcshow: string
  /** 上课计划ID */
  skjhid: number
  /** 课时 */
  ks: number
  /** 学院代码 */
  ssxy: string
  /** 系部代码 */
  ssxb: string
  /** 系部名称 */
  ssxbmc: string
  /** 班级代码 */
  ssbj: string
  /** 班级名称 */
  ssbjmc: string
  /** 学生学号 */
  xsxh: string
  /** 座位号 */
  zwh: number
  /** 学生姓名 */
  xsxm: string
  /** 考勤其他状态 */
  kqqtzt: number
  /** 考勤提交人 */
  kqtjry: string
  /** 考勤提交人姓名 */
  kqtjryxm: string
  /** 考勤时间 */
  kqsj: string
  /** 考勤其他时间 */
  kqqtsj: string
  /** 管理请假 */
  glqj: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 临时上报 */
  lysb: number
  /** 是否及时考勤 */
  sfjcks: number
  /** ID列表 */
  ids: string
  /** 考勤状态0 */
  kqzt0: string
  /** 考勤状态1 */
  kqzt1: string
}

/**
 * 教师考勤列表响应
 */
export interface TeacherAttendListResponse {
  /** 考勤学生列表 */
  items: TeacherAttendItem[]
  /** 总数 */
  total: number
}

/**
 * 教师课程安排详情接口参数
 */
export interface TeacherScheduleInfoParams {
  /** 课程安排ID */
  id: number
}

/**
 * 教师课程安排详情内部任务信息
 */
export interface TeacherScheduleTaskInfo {
  /** ID */
  id: string
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 课程名称 */
  kcmc: string
  /** 学期总学时 */
  xqzxs: number
  /** 总学时 */
  zxs: number
  /** 周数 */
  zs: number
}

/**
 * 教师课程安排详情信息
 */
export interface TeacherScheduleInfo {
  /** ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
  /** 周次 */
  zc: number
  /** 上课日期 */
  skrq: string
  /** 上课开始时间 */
  skkssj: string
  /** 上课结束时间 */
  skjssj: string
  /** 星期 */
  xqs: number
  /** 节次 */
  jc: string
  /** 显示节次 */
  jcshow: string
  /** 上课内容 */
  sknl: string
  /** 上课方式 */
  skfs: string
  /** 上课方式名称 */
  skfsmc: string
  /** 作业提示 */
  zyts: number
  /** 作业评估方式 */
  zypgfs: string
  /** 作业评估方式名称 */
  zypgfsmc: string
  /** 辅助人数 */
  fzrszs: string
  /** 实验仪器设备数量 */
  syyqsbsl: string
  /** 上课场地类型 */
  skcdlx: string
  /** 上课场地代码 */
  skcddm: string
  /** 上课场地名称 */
  skcdmc: string
  /** 上课教师 */
  skjs: string
  /** 上课教师姓名 */
  skjsxm: string
  /** 上课班级 */
  skbj: string
  /** 上课班级名称 */
  skbjmc: string
  /** 实验人数 */
  syrs: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 课时 */
  ks: number
  /** 学制 */
  xz: number
  /** 关联ID */
  glid: number
  /** 教学计划中心状态 */
  skjhzxzt: number
  /** 工作量录入状态 */
  skjhgzlrdzt: number
  /** 作业内容 */
  zynr: string
  /** 教师确认状态 */
  skjhjsqrzt: number
  /** 教师确认时间 */
  skjhjsqrsj: string
  /** 教师确认人编号 */
  skjhjsqrrybh: string
  /** 教师确认人姓名 */
  skjhjsqrryxm: string
  /** 学生确认状态 */
  skjhxsqrzt: number
  /** 学生确认时间 */
  skjhxsqrsj: string
  /** 学生确认学号 */
  skjhxsqrxsxh: string
  /** 学生确认姓名 */
  skjhxsqrxsxm: string
  /** 迟到上报状态 */
  cdsbzt: number
  /** 迟到上报事由情况 */
  cdsbsyqk: string
  /** 审批状态 */
  spzt: number
  /** 网络教学关联ID */
  wljxglid: number
  /** 学生负责ID */
  xsfzid: number
  /** 学生负责名称 */
  xsfzmc: string
  /** 附件类别 */
  fjlb: string
  /** 考勤照片 */
  kqzp: string
  /** 调整补课申请状态 */
  ttbksqzt: number
  /** 迟到确认审核状态 */
  csqrshzt: number
  /** 作业附件类别 */
  zyfjlb: string
  /** 直播平台代码 */
  zbptdm: string
  /** 直播平台名称 */
  zbptmc: string
  /** 直播平台内容 */
  zbptnr: string
  /** 教学平台代码 */
  jxptdm: string
  /** 教学平台名称 */
  jxptmc: string
  /** 教学平台内容 */
  jxptnr: string
  /** 学生刷课审核状态 */
  xsskshzt: number
  /** 教学任务 */
  jxrw: TeacherScheduleTaskInfo
  /** 节次开始时间 */
  jckssj: string
  /** 节次结束时间 */
  jcjssj: string
  /** 考勤节次信息 */
  kqjcInfo: string[]
  /** 确认按钮 */
  qrbtn: number
  /** 上课确认开始时间 */
  skqrkssj: string
  /** 上课确认结束时间 */
  skqrjssj: string
  /** 迟到确认按钮 */
  csqrbtn: number
  /** 实训课信息ID */
  sxkxx_id?: number
  /** 实训课使用对象 */
  sxkxx_sydx?: number
  /** 实训课教学任务ID */
  sxkxx_jxrwid?: number
  /** 实训课课程安排ID */
  sxkxx_skjhid?: number
  /** 实训课任务名称 */
  sxkxx_rwmc?: string
  /** 实训课子任务名称 */
  sxkxx_zrwmc?: string
  /** 实训课环节名称 */
  sxkxx_hjmc?: string
  /** 实训课环节分值 */
  sxkxx_hjfz?: string
  /** 实训课评价指标描述 */
  sxkxx_pjzbms?: string
  /** 实训课备注 */
  sxkxx_remark?: string
  /** 实训课创建时间 */
  sxkxx_create_time?: number
  /** 实训课更新时间 */
  sxkxx_update_time?: number
  /** 实训课删除标记 */
  sxkxx_deltag?: number
  /** 实训课操作员编号 */
  sxkxx_oprybh?: string
  /** 实训课技能要求 */
  sxkxx_sxjnyq?: string
  /** 实训课考核方式 */
  sxkxx_sxkhfs?: string
  /** 实训课模块数量 */
  sxkxx_mksl?: number
  /** 实训课其他实训地点 */
  sxkxx_qtsxdd?: string
  /** 实训课配套实训资源名称 */
  sxkxx_ptsxzymc?: string
  /** 实训课是否对外服务 */
  sxkxx_sfdwfw?: number
  /** 实训课是否虚拟仿真实训 */
  sxkxx_sfxnfzsx?: number
  /** 是否是今天的课程 */
  isToday?: boolean
  /** 提示类型 */
  tip_type?: string
  /** 提示文本 */
  tip_text?: string
  /** 考勤权限 */
  kqqx?: number
}

/**
 * 更新教师考勤请求参数
 */
export interface UpdateTeacherAttendRequest {
  /** 上课计划ID */
  skjhid: number
  /** 考勤学生列表 */
  rows: UpdateTeacherAttendRow[]
}

/**
 * 更新教师考勤学生行项目
 */
export interface UpdateTeacherAttendRow {
  /** 学生学号 */
  xsxh: string
  /** 备注 */
  remark?: string
  /** 考勤状态0 */
  kqzt0: string
  /** 考勤状态1 */
  kqzt1: string
}

/**
 * 更新教师考勤响应
 */
export interface UpdateTeacherAttendResponse {
  /** 响应代码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 响应数据 */
  data: any
}

/**
 * 更新教师课程安排请求参数
 */
export interface UpdateTeacherScheduleRequest {
  /** 上课方式代码 */
  skfs: string
  /** 作业评估方式代码 */
  zypgfs: string
  /** 作业题数 */
  zyts: number
  /** 场地设备状态 0-正常 1-异常 */
  cdsbzt: number
  /** 实训课程模块数量 */
  sxkxx_mksl: number
  /** 是否虚拟仿真实训 0-否 1-是 */
  sxkxx_sfxnfzsx: number
  /** 是否对外服务 0-否 1-是 */
  sxkxx_sfdwfw: number
  /** 确认按钮 */
  qrbtn: number
  /** 上课开始时间 */
  skkssj: string
  /** 上课教师姓名 */
  skjsxm: string
  /** 上课场地名称 */
  skcdmc: string
  /** 上课内容 */
  sknl: string
  /** 作业内容 */
  zynr: string
  /** 场地设备使用情况 */
  cdsbsyqk: string
  /** 实训课程任务名称 */
  sxkxx_zrwmc: string
  /** 实训技能要求 */
  sxkxx_sxjnyq: string
  /** 配套实训资源名称 */
  sxkxx_ptsxzymc: string
  /** 实训考核方式 */
  sxkxx_sxkhfs: string
  /** 其他实训地点 */
  sxkxx_qtsxdd: string
  /** 实训备注 */
  sxkxx_remark: string
  /** 教学任务ID */
  jxrwid: number
  /** 课程安排ID */
  id: number
}

/**
 * 更新教师课程安排响应
 */
export interface UpdateTeacherScheduleResponse {
  /** 响应代码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 响应数据 */
  data: any
}

/**
 * 教师课程表统计数据
 */
export interface TeacherScheduleStatistics {
  /** 任务数量 */
  rws: number
  /** 总节次 */
  zjc: number
  /** 教师确认节次 */
  jsqrjc: number
  /** 教师未确认节次 */
  jswqrjc: number
  /** 学生未确认节次 */
  xswqrjc: number
  /** 已认定节次 */
  rdjc: number
  /** 认定学时 */
  rdxs: number
  /** 未认定节次 */
  wrdjc: number
  /** 已执行节次 */
  yzxjc: number
  /** 执行率 */
  zxl: number
  /** 未执行节次 */
  wzxjc: number
}

/**
 * 教学任务总分同步请求参数
 */
export interface TeachingTaskTotalScoreSyncRequest {
  /** 教学任务ID */
  jxrwid: string
}

/**
 * 教学任务总分同步响应
 */
export interface TeachingTaskTotalScoreSyncResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 前端消息 */
  frontMsg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: any[]
}

/**
 * 教学任务总分更新请求参数
 */
export interface TeachingTaskTotalScoreUpdateRequest {
  /** 教学任务ID */
  jxrwid: string
  /** 成绩行数据 */
  rows: TeachingTaskTotalScoreRow[]
}

/**
 * 教学任务总分行数据
 */
export interface TeachingTaskTotalScoreRow {
  /** 记录ID */
  id: number
  /** 成绩 */
  cj: string
}

/**
 * 教学任务总分更新响应数据项
 */
export interface TeachingTaskTotalScoreItem {
  /** 记录ID */
  id: number
  /** 所属班级 */
  ssbj: string
  /** 所属班级名称 */
  ssbjmc: string
  /** 学生学号 */
  xsxh: string
  /** 座位号 */
  zwh: number
  /** 姓名 */
  xm: string
  /** 教学任务ID */
  jxrwid: number
  /** 选课信息ID */
  xkxxid: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: string
  /** 主讲教师 */
  zdjs: string
  /** 主讲教师姓名 */
  zdjsxm: string
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 课程性质 */
  kcxz: string
  /** 课程学分 */
  kcxf: number
  /** 成绩评定类型 */
  cjpdlx: string
  /** 成绩 */
  cj: string
  /** 百分制成绩 */
  bfzcj: number
  /** 通过标志 */
  tgbz: number
  /** 成绩绩点 */
  cjjd: number
  /** 成绩上限 */
  cjsx: string
  /** 成绩提交标志 */
  cjtjbz: number
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 成绩提交人员 */
  cjtjry: string
  /** 成绩提交时间 */
  cjtjsj: string
}

/**
 * 教学任务总分更新响应
 */
export interface TeachingTaskTotalScoreUpdateResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: TeachingTaskTotalScoreItem[]
}

/**
 * 教师日常评分标记列表查询参数
 */
export interface DailyScoreMarkListQuery {
  /** 当前页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 教学任务ID */
  jxrwid: number
}

/**
 * 教师日常评分标记项
 */
export interface DailyScoreMarkItem {
  /** ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
  /** 日常成绩类型代码 */
  rccjlxdm: string
  /** 日常成绩类型名称 */
  rccjlxmc: string
  /** 日常成绩登记编号 */
  rccjdjbh: number
  /** 日常成绩开始时间(登记日期) */
  rccjkssj: string
  /** 日常成绩开始周次 */
  rccjkszc: number
  /** 日常成绩说明 */
  rccjsm: string
  /** 日常成绩提交时间 */
  rccjtjsj: string
  /** 成绩提交标志 (0-未提交, 1-已提交) */
  cjtjbz: number
  /** 成绩平台类型 */
  cjptlx: string
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 日常成绩查询状态 (0-不开放查询, 1-开放查询) */
  rccjcxzt: number
  /** 工作ID */
  work_id: number
}

/**
 * 教师日常评分标记列表响应
 */
export interface DailyScoreMarkListResponse {
  /** 日常评分标记列表 */
  items: DailyScoreMarkItem[]
  /** 总记录数 */
  total: number
  /** 分页信息 */
  page: PageInfo
}

/**
 * 日常成绩标记编辑查询参数
 */
export interface DailyScoreMarkEditQuery {
  /** 记录ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
}

/**
 * 日常成绩标记详情
 */
export interface DailyScoreMarkDetail {
  /** ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
  /** 日常成绩类型代码 */
  rccjlxdm: string
  /** 日常成绩类型名称 */
  rccjlxmc: string
  /** 日常成绩登记编号 */
  rccjdjbh: number
  /** 日常成绩开始时间(登记日期) */
  rccjkssj: string
  /** 日常成绩开始周次 */
  rccjkszc: number
  /** 日常成绩说明 */
  rccjsm: string
  /** 日常成绩提交时间 */
  rccjtjsj: string
  /** 成绩提交标志 (0-未提交, 1-已提交) */
  cjtjbz: number
  /** 成绩平台类型 */
  cjptlx: string
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 日常成绩查询状态 (0-不开放查询, 1-开放查询) */
  rccjcxzt: number
  /** 工作ID */
  work_id: number
}

/**
 * 更新日常成绩标记请求参数
 */
export interface UpdateDailyScoreMarkRequest {
  /** 日常成绩查询状态 (0-不开放查询, 1-开放查询) */
  rccjcxzt: number
  /** 记录ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
  /** 日常成绩类型代码 */
  rccjlxdm: string
  /** 日常成绩登记编号 */
  rccjdjbh: number
  /** 日常成绩开始时间(登记日期) */
  rccjkssj: string
  /** 日常成绩说明 */
  rccjsm: string
}

/**
 * 更新日常成绩标记响应
 */
export interface UpdateDailyScoreMarkResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: null
}

/**
 * 添加日常成绩标记请求参数
 */
export interface AddDailyScoreMarkRequest {
  /** 日常成绩查询状态 (0-不开放查询, 1-开放查询) */
  rccjcxzt: number
  /** 教学任务ID */
  jxrwid: string | number
  /** 日常成绩类型代码 */
  rccjlxdm: string
  /** 日常成绩登记编号 */
  rccjdjbh: number
  /** 日常成绩开始时间(登记日期) */
  rccjkssj: string
  /** 日常成绩说明 */
  rccjsm: string
}

/**
 * 删除日常成绩请求参数
 */
export interface DeleteDailyScoreMarkRequest {
  /** 要删除的日常成绩ID数组 */
  ids: number[]
}

/**
 * 日常分数类型信息查询参数
 */
export interface DailyScoreTypeInfoQuery {
  /** 教学任务ID */
  jxrwid: string | number
  /** 日常成绩登记点ID */
  rccjdjid: string | number
  /** 类型 */
  type: string
}

/**
 * 日常分数类型信息响应数据
 */
export interface DailyScoreTypeInfoResponse {
  /** 类型编码 */
  type: string
  /** 类型名称 */
  name: string
  /** 分数选项列表 */
  options: ScoreOption[]
  /** 最大分数值 */
  max: number
  /** 时间范围 */
  time: ScoreTimeRange[]
  /** 是否已提交 0-未提交 1-已提交 */
  submit: number
  /** 是否已申请 0-未申请 1-已申请 */
  apply: number
  /** 是否允许 0-不允许 1-允许 */
  allow: number
  /** 是否检查 0-不检查 1-检查 */
  check: number
}

/**
 * 日常成绩状态修改请求
 */
export interface DailyScoreStatusEditRequest {
  /** 日常成绩ID数组 */
  ids: number[]
  /** 成绩提交标志 (0-未提交, 1-已提交) */
  cjtjbz: number
  /** 日常成绩查询状态 (0-不开放查询, 1-开放查询) */
  rccjcxzt: number
}

/**
 * 日常成绩评定类型设置请求参数
 */
export interface DailyScoreTypeSetRequest {
  /** 教学任务ID */
  jxrwid: string
  /** 日常成绩登记点ID */
  rccjdjid: number
  /** 成绩评定类型 */
  cjptlx: string
}

/**
 * 日常成绩更新请求参数
 */
export interface DailyScoreUpdateRequest {
  /** 教学任务ID */
  jxrwid: string
  /** 日常成绩登记点ID */
  rccjdjid: number
  /** 成绩行数据 */
  rows: DailyScoreUpdateRow[]
}

/**
 * 日常成绩更新行数据
 */
export interface DailyScoreUpdateRow {
  /** 记录ID */
  id: number
  /** 成绩 */
  cj: string
}

/**
 * 日常成绩更新响应数据项
 */
export interface DailyScoreUpdateItem {
  /** 记录ID */
  id: number
  /** 日常成绩登记点ID */
  rccjdjid: number
  /** 所属班级 */
  ssbj: string
  /** 所属班级名称 */
  ssbjmc: string
  /** 学生学号 */
  xsxh: string
  /** 座位号 */
  zwh: number
  /** 姓名 */
  xm: string
  /** 教学任务ID */
  jxrwid: number
  /** 选课信息ID */
  xkxxid: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 主讲教师 */
  zdjs: string
  /** 主讲教师姓名 */
  zdjsxm: string
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 课程性质 */
  kcxz: string
  /** 课程学分 */
  kcxf: number
  /** 成绩评定类型 */
  cjpdlx: string
  /** 成绩 */
  cj: string
  /** 百分制成绩 */
  bfzcj: number
  /** 通过标志 */
  tgbz: number
  /** 成绩绩点 */
  cjjd: number
  /** 成绩上限 */
  cjsx: string
  /** 成绩提交标志 */
  cjtjbz: number
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 成绩提交人员 */
  cjtjry: string
  /** 成绩提交时间 */
  cjtjsj: string
}

/**
 * 日常成绩更新响应
 */
export interface DailyScoreUpdateResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: DailyScoreUpdateItem[]
}

/**
 * 总评成绩撤销申请详情请求参数
 */
export interface TotalScoreRevokeDetailQuery {
  /**
   * 教学任务ID
   */
  jxrwid: string
  /**
   * 申请类型（成绩撤销申请）
   */
  sqlx: string
}

/**
 * 总评成绩撤销申请详情响应
 */
export interface TotalScoreRevokeDetailResponse {
  /**
   * 教学任务ID
   */
  id: number
  /**
   * 学年
   */
  xn: string
  /**
   * 学期
   */
  xq: number
  /**
   * 课程名称
   */
  kcmc: string
  /**
   * 所属学部名称
   */
  ssxbmc: string
  /**
   * 所属教研室名称
   */
  ssjysmc: string
  /**
   * 班级名称
   */
  bjmc: string
  /**
   * 主讲教师姓名
   */
  zdjsxm: string
  /**
   * 成绩提交标志
   */
  cjtjbz: number
  /**
   * 学期总学时
   */
  xqzxs: string
  /**
   * 总学时
   */
  zxs: string
  /**
   * 总数
   */
  zs: number
  /**
   * 考核方式
   */
  khfs: string
  /**
   * 原单申请表ID
   */
  ydsqbid: number
  /**
   * 申请原因
   */
  sqyy: string
  /**
   * 申请附件列表
   */
  sqfjlb: string
}

/**
 * 总评成绩撤销申请请求参数
 */
export interface TotalScoreRevokeApplyRequest {
  /**
   * 教学任务ID
   */
  id: number
  /**
   * 申请类型（成绩撤销申请）
   */
  sqlx: string
  /**
   * 申请原因
   */
  sqyy: string
  /**
   * 申请附件列表，格式为文件路径|文件名，多个附件用逗号分隔
   */
  sqfjlb: string
}

/**
 * 总评成绩撤销申请响应
 */
export interface TotalScoreRevokeApplyResponse {
  /**
   * 状态码
   */
  code: number
  /**
   * 消息
   */
  msg: string
  /**
   * 时间戳
   */
  time: number
  /**
   * 数据
   */
  data: null
  frontMsg: string
}

/**
 * 授课要点详情接口返回数据
 */
export interface TeachingPointDetail {
  /**
   * 授课要点ID
   */
  id: number
  /**
   * 班级名称
   */
  bjmc: string
  /**
   * 学生学习本课程必备的知识和技能
   */
  bbzsjn: string
  /**
   * 本课程为后续课程提供的知识
   */
  hxkctgzs: string
  /**
   * 教学大纲名称/版本
   */
  kcjxdgxx: string
  /**
   * 附件列表
   */
  kcbzfj: Array<{
    id: string
    name: string
    url: string
  }>
  /**
   * 知识目标
   */
  kcjyzsmb: string
  /**
   * 素质目标
   */
  kcjyszmb: string
  /**
   * 本课程的考核办法
   */
  kckhbf: string
  /**
   * 课程名称
   */
  kcmc: string
  /**
   * 本课程为后续课程提供的技能
   */
  hxkctgjn: string
  /**
   * 能力目标
   */
  kcjynlmb: string
}

/**
 * 更新授课要点请求参数
 */
export interface UpdateTeachingPointRequest {
  /**
   * 附件列表，格式为[{id, name, url}]
   */
  kcbzfj: Array<{
    id: string
    name: string
    url: string
  }>
  /**
   * 学生学习本课程必备的知识和技能
   */
  bbzsjn: string
  /**
   * 本课程为后续课程提供的知识
   */
  hxkctgzs: string
  /**
   * 本课程为后续课程提供的技能
   */
  hxkctgjn: string
  /**
   * 教学大纲名称/版本
   */
  kcjxdgxx: string
  /**
   * 知识目标
   */
  kcjyzsmb: string
  /**
   * 能力目标
   */
  kcjynlmb: string
  /**
   * 素质目标
   */
  kcjyszmb: string
  /**
   * 本课程的考核办法
   */
  kckhbf: string
}

/**
 * 更新授课要点响应
 */
export interface UpdateTeachingPointResponse {
  /**
   * 响应代码
   */
  code: number
  /**
   * 响应消息
   */
  msg: string
  /**
   * 时间戳
   */
  time: number
  /**
   * 响应数据
   */
  data: {
    /**
     * 学生学习本课程必备的知识和技能
     */
    bbzsjn: string
    /**
     * 本课程为后续课程提供的知识
     */
    hxkctgzs: string
    /**
     * 教学大纲名称/版本
     */
    kcjxdgxx: string
    /**
     * 附件列表，格式为"id|name|url"
     */
    kcbzfj: string
    /**
     * 知识目标
     */
    kcjyzsmb: string
    /**
     * 素质目标
     */
    kcjyszmb: string
    /**
     * 本课程的考核办法
     */
    kckhbf: string
    /**
     * 本课程为后续课程提供的技能
     */
    hxkctgjn: string
    /**
     * 能力目标
     */
    kcjynlmb: string
    /**
     * 更新时间
     */
    update_time: number
  }
}

/**
 * 教师课程安排确认接口返回的教学任务信息
 */
export interface TeacherScheduleApplyConfirmTaskInfo {
  /** ID */
  id: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 课程名称 */
  kcmc: string
  /** 学期总学时 */
  xqzxs: string
  /** 总学时 */
  zxs: string
  /** 周数 */
  zs: number
  /** 关联教学平台 */
  gljxpt: string
  /** 主讲教师姓名 */
  zdjsxm: string
  /** 开始周 */
  ksz: number
  /** 结束周 */
  jsz: number
  /** 所属学部 */
  ssxb: string
  /** 所属学部名称 */
  ssxbmc: string
  /** 所属教研室 */
  ssjys: string
  /** 所属教研室名称 */
  ssjysmc: string
}

/**
 * 教师课程安排确认接口返回数据
 */
export interface TeacherScheduleApplyConfirmResponse {
  /** ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
  /** 周次 */
  zc: number
  /** 上课日期 */
  skrq: string
  /** 上课开始时间 */
  skkssj: string
  /** 上课结束时间 */
  skjssj: string
  /** 星期 */
  xqs: number
  /** 节次 */
  jc: string
  /** 显示节次 */
  jcshow: string
  /** 上课内容 */
  sknl: string
  /** 上课方式 */
  skfs: string
  /** 上课方式名称 */
  skfsmc: string
  /** 作业提示 */
  zyts: number
  /** 作业评估方式 */
  zypgfs: string
  /** 作业评估方式名称 */
  zypgfsmc: string
  /** 辅助人数 */
  fzrszs: string
  /** 实验仪器设备数量 */
  syyqsbsl: string
  /** 上课场地类型 */
  skcdlx: string
  /** 上课场地代码 */
  skcddm: string
  /** 上课场地名称 */
  skcdmc: string
  /** 上课教师 */
  skjs: string
  /** 上课教师姓名 */
  skjsxm: string
  /** 上课班级 */
  skbj: string
  /** 上课班级名称 */
  skbjmc: string
  /** 实验人数 */
  syrs: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 课时 */
  ks: number
  /** 学制 */
  xz: number
  /** 关联ID */
  glid: number
  /** 教学计划中心状态 */
  skjhzxzt: number
  /** 工作量录入状态 */
  skjhgzlrdzt: number
  /** 作业内容 */
  zynr: string
  /** 教师确认状态 */
  skjhjsqrzt: number
  /** 教师确认时间 */
  skjhjsqrsj: string
  /** 教师确认人编号 */
  skjhjsqrrybh: string
  /** 教师确认人姓名 */
  skjhjsqrryxm: string
  /** 学生确认状态 */
  skjhxsqrzt: number
  /** 学生确认时间 */
  skjhxsqrsj: string
  /** 学生确认学号 */
  skjhxsqrxsxh: string
  /** 学生确认姓名 */
  skjhxsqrxsxm: string
  /** 迟到上报状态 */
  cdsbzt: number
  /** 迟到上报事由情况 */
  cdsbsyqk: string
  /** 审批状态 */
  spzt: number
  /** 网络教学关联ID */
  wljxglid: number
  /** 学生负责ID */
  xsfzid: number
  /** 学生负责名称 */
  xsfzmc: string
  /** 附件类别 */
  fjlb: any[]
  /** 考勤照片 */
  kqzp: string
  /** 调整补课申请状态 */
  ttbksqzt: number
  /** 迟到确认审核状态 */
  csqrshzt: number
  /** 作业附件类别 */
  zyfjlb: string
  /** 直播平台代码 */
  zbptdm: string
  /** 直播平台名称 */
  zbptmc: string
  /** 直播平台内容 */
  zbptnr: string
  /** 教学平台代码 */
  jxptdm: string
  /** 教学平台名称 */
  jxptmc: string
  /** 教学平台内容 */
  jxptnr: string
  /** 学生刷课审核状态 */
  xsskshzt: number
  /** 教学任务 */
  jxrw: TeacherScheduleApplyConfirmTaskInfo
  /** 是否是今天的课程 */
  isToday: boolean
  sqyy: string
  sqfjlb: string
}

/**
 * 提交课程安排确认请求参数
 */
export interface SubmitTeacherScheduleApplyConfirmRequest {
  /** 附件列表，格式为文件路径|文件名，多个附件用逗号分隔 */
  sqfjlb: string
  /** 课程安排ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
  /** 申请原因 */
  sqyy: string
}

/**
 * 提交课程安排确认响应
 */
export interface SubmitTeacherScheduleApplyConfirmResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: null
}

/**
 * 保存教学进度请求参数
 */
export interface SaveTeachingProcessRequest {
  /** 教学任务ID */
  jxrwid: string
  /** 教学进度数据 */
  data: TeachingProcessItem[]
}

/**
 * 教学进度项
 */
export interface TeachingProcessItem {
  /** ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
  /** 周次 */
  zc: number
  /** 上课日期 */
  skrq: string
  /** 上课开始时间 */
  skkssj: string
  /** 上课结束时间 */
  skjssj: string
  /** 星期 */
  xqs: number
  /** 节次 */
  jc: string
  /** 显示节次 */
  jcshow: string
  /** 上课内容 */
  sknl: string
  /** 上课方式 */
  skfs: string
  /** 上课方式名称 */
  skfsmc: string
  /** 作业题数 */
  zyts: number
  /** 作业评估方式 */
  zypgfs: string
  /** 作业评估方式名称 */
  zypgfsmc: string
  /** 辅助人数 */
  fzrszs: string
  /** 实验仪器设备数量 */
  syyqsbsl: string
  /** 上课场地类型 */
  skcdlx: string
  /** 上课场地代码 */
  skcddm: string | null
  /** 上课场地名称 */
  skcdmc: string | null
  /** 上课教师 */
  skjs: string
  /** 上课教师姓名 */
  skjsxm: string
  /** 上课班级 */
  skbj: string
  /** 上课班级名称 */
  skbjmc: string
  /** 实验人数 */
  syrs: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 课时 */
  ks: number
  /** 学制 */
  xz: number
  /** 关联ID */
  glid: number
  /** 教学计划中心状态 */
  skjhzxzt: number
  /** 工作量录入状态 */
  skjhgzlrdzt: number
  /** 作业内容 */
  zynr: string
  /** 教师确认状态 */
  skjhjsqrzt: number
  /** 教师确认时间 */
  skjhjsqrsj: string
  /** 教师确认人编号 */
  skjhjsqrrybh: string | null
  /** 教师确认人姓名 */
  skjhjsqrryxm: string | null
  /** 学生确认状态 */
  skjhxsqrzt: number
  /** 学生确认时间 */
  skjhxsqrsj: string
  /** 学生确认学号 */
  skjhxsqrxsxh: string | null
  /** 学生确认姓名 */
  skjhxsqrxsxm: string | null
  /** 迟到上报状态 */
  cdsbzt: number
  /** 迟到上报事由情况 */
  cdsbsyqk: string
  /** 审批状态 */
  spzt: number
  /** 网络教学关联ID */
  wljxglid: number
  /** 学生分组ID */
  xsfzid: number
  /** 学生分组名称 */
  xsfzmc: string
  /** 附件列表 */
  fjlb: string
  /** 考勤照片 */
  kqzp: string
  /** 调停补课申请状态 */
  ttbksqzt: number
  /** 迟到确认审核状态 */
  csqrshzt: number
  /** 作业附件列表 */
  zyfjlb: string
  /** 直播平台代码 */
  zbptdm: string | null
  /** 直播平台名称 */
  zbptmc: string | null
  /** 直播平台内容 */
  zbptnr: string | null
  /** 教学平台代码 */
  jxptdm: string | null
  /** 教学平台名称 */
  jxptmc: string | null
  /** 教学平台内容 */
  jxptnr: string | null
  /** 学生刷课审核状态 */
  xsskshzt: number
  /** 序号 */
  xh?: number
  /** 场地按钮 */
  roomBtn?: boolean
}

/**
 * 保存教学进度响应
 */
export interface SaveTeachingProcessResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 响应数据 */
  data: {
    /** 教学任务信息 */
    jxrw: {
      /** 任务ID */
      id: number
      /** 计划ID */
      ssjh: number
      /** 学年 */
      xn: string
      /** 学期 */
      xq: number
      /** 课程代码 */
      kcdm: string
      /** 课程名称 */
      kcmc: string
      /** 主讲教师 */
      zdjs: string
      /** 主讲教师姓名 */
      zdjsxm: string
      /** 其他辅导教师姓名 */
      qtfdjsxm: string
      /** 其他辅导教师 */
      qtfdjs: string
      /** 班级 */
      ssbj: string
      /** 班级名称 */
      bjmc: string
    }
    /** 显示按钮 */
    showBtn: boolean
    /** 授课教师列表 */
    skjs: Array<{
      /** 教师编号 */
      value: string
      /** 教师姓名 */
      label: string
    }>
    /** 辅导教师列表 */
    fdjs: Array<{
      /** 教师编号 */
      value: string
      /** 教师姓名 */
      label: string
    }>
    /** 授课方式列表 */
    skfs: Array<{
      /** 授课方式代码 */
      value: string
      /** 授课方式名称 */
      label: string
    }>
    /** 节次列表 */
    jcList: Array<{
      /** 节次值 */
      value: string
      /** 节次名称 */
      label: string
    }>
    /** 授课计划列表 */
    skjhList: TeachingProcessItem[]
    /** 学生分组列表 */
    xsfz: Array<{
      /** 学生分组ID */
      value: number
      /** 学生分组名称 */
      label: string
    }>
    /** 作业代码列表 */
    zydm: Array<{
      /** 作业代码 */
      value: string
      /** 作业名称 */
      label: string
    }>
    /** 分组信息 */
    fzxx: any[]
    /** 场地类型列表 */
    cdlx: Array<{
      /** 场地类型代码 */
      value: string
      /** 场地类型名称 */
      label: string
    }>
    /** 课程列表 */
    courseList: Array<{
      /** 课程ID */
      id: number
      /** 课程名称 */
      name: string
    }>
  }
}

/**
 * 保存授课计划请求参数 - 基于curl请求数据结构
 */
export interface SaveTeachingPlanRequest {
  /** 计划ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
  /** 周次 */
  zc: number
  /** 上课日期 */
  skrq: string
  /** 上课开始时间 */
  skkssj: string
  /** 上课结束时间 */
  skjssj: string
  /** 星期几 */
  xqs: number
  /** 节次 */
  jc: string
  /** 节次显示 */
  jcshow: string
  /** 授课内容 */
  sknl: string
  /** 授课方式 */
  skfs: string
  /** 授课方式名称 */
  skfsmc: string
  /** 作业题数 */
  zyts: number
  /** 作业评估方式 */
  zypgfs: string
  /** 作业评估方式名称 */
  zypgfsmc: string
  /** 负责人所在组 */
  fzrszs: string
  /** 使用仪器设备数量 */
  syyqsbsl: string
  /** 上课场地类型 */
  skcdlx: string
  /** 上课场地代码 */
  skcddm: string
  /** 上课场地名称 */
  skcdmc: string
  /** 上课教师 */
  skjs: string
  /** 上课教师姓名 */
  skjsxm: string
  /** 上课班级 */
  skbj: string
  /** 上课班级名称 */
  skbjmc: string
  /** 实验人数 */
  syrs: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 课时 */
  ks: number
  /** 学制 */
  xz: number
  /** 关联ID */
  glid: number
  /** 教学计划中心状态 */
  skjhzxzt: number
  /** 工作量录入状态 */
  skjhgzlrdzt: number
  /** 作业内容 */
  zynr: string
  /** 教师确认状态 */
  skjhjsqrzt: number
  /** 教师确认时间 */
  skjhjsqrsj: string
  /** 教师确认人编号 */
  skjhjsqrrybh: string
  /** 教师确认人姓名 */
  skjhjsqrryxm: string
  /** 学生确认状态 */
  skjhxsqrzt: number
  /** 学生确认时间 */
  skjhxsqrsj: string
  /** 学生确认学号 */
  skjhxsqrxsxh: string
  /** 学生确认姓名 */
  skjhxsqrxsxm: string
  /** 迟到上报状态 */
  cdsbzt: number
  /** 迟到上报事由情况 */
  cdsbsyqk: string
  /** 审批状态 */
  spzt: number
  /** 网络教学关联ID */
  wljxglid: number
  /** 学生分组ID */
  xsfzid: number
  /** 学生分组名称 */
  xsfzmc: string
  /** 附件类别 */
  fjlb: string
  /** 考勤照片 */
  kqzp: string
  /** 调停补课申请状态 */
  ttbksqzt: number
  /** 迟到确认审核状态 */
  csqrshzt: number
  /** 作业附件类别 */
  zyfjlb: string
  /** 直播平台代码 */
  zbptdm: string
  /** 直播平台名称 */
  zbptmc: string
  /** 直播平台内容 */
  zbptnr: string
  /** 教学平台代码 */
  jxptdm: string
  /** 教学平台名称 */
  jxptmc: string
  /** 教学平台内容 */
  jxptnr: string
  /** 学生刷课审核状态 */
  xsskshzt: number
  /** 场地按钮 */
  roomBtn?: boolean
  /** 序号 */
  xh?: number
}

/**
 * 保存授课计划响应
 */
export interface SaveTeachingPlanResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 响应数据 */
  data: null
}

/**
 * 教学检查查询参数
 */
export interface TeachingCheckQuery {
  /** 教学任务ID */
  jxrwid: string | number
  /** 操作类型 */
  opType: number
}

/**
 * 教学检查数据
 */
export interface TeachingCheckData {
  /** 是否规范 */
  qcjxjctxx0?: string
  /** 备课周数 */
  qcjxjctxx1?: string
  /** 是否新教案 */
  qcjxjctxx2?: string
  /** 是否详案 */
  qcjxjctxx3?: string
  /** 教案首页填写是否规范 */
  qcjxjctxx4?: string
  /** 是否体现课程教学大纲的要求 */
  qcjxjctxx5?: string
  /** 是否体现学科或生产的最新成果 */
  qcjxjctxx6?: string
  /** 突出技术应用能力培养情况 */
  qcjxjctxx7?: string
  /** 备注 */
  qcjxjctxx8?: string
  /** 期中教学计划课时 */
  qzjxjctxx0?: string
  /** 期中教学超前或滞后课时 */
  qzjxjctxx1?: string
  /** 期中教学计划作业批改 */
  qzjxjctxx2?: string
  /** 期中教学实际作业批改 */
  qzjxjctxx3?: string
  /** 期中教学批改情况 */
  qzjxjctxx4?: string
  /** 期中教学计划实验次数 */
  qzjxjctxx5?: string
  /** 期中教学实际实验次数 */
  qzjxjctxx6?: string
  /** 期中教学计划辅导次数 */
  qzjxjctxx7?: string
  /** 期中教学实际辅导次数 */
  qzjxjctxx8?: string
  /** 期中教学小测次数 */
  qzjxjctxx9?: string
  /** 期中教学阶段考次数 */
  qzjxjctxx10?: string
  /** 期中教学学生接受情况 */
  qzjxjctxx11?: string
  /** 期中教学听课次数 */
  qzjxjctxx12?: string
  /** 期中教学教案编写情况 */
  qzjxjctxx13?: string
  /** 期中教学备注 */
  qzjxjctxx14?: string
  /** 计划课时 */
  qmjxjctxx0?: string
  /** 实际完成课时 */
  qmjxjctxx1?: string
  /** 作业布置计划次数 */
  qmjxjctxx2?: string
  /** 作业布置实际次数 */
  qmjxjctxx3?: string
  /** 作业全批全改次数 */
  qmjxjctxx4?: string
  /** 是否按规定完成教案编写 */
  qmjxjctxx5?: string
  /** 听课完成次数 */
  qmjxjctxx6?: string
  /** 何时参加何种进修 */
  qmjxjctxx7?: string
  /** 是否本学期新增的"双师"教师 */
  qmjxjctxx8?: string
  /** 参加何种教科研项目及完成情况 */
  qmjxjctxx9?: string
  /** 参编何种高职教材及完成情况 */
  qmjxjctxx10?: string
  /** 开设讲座的主题、对象及时间 */
  qmjxjctxx11?: string
  /** 是否按要求填《教师工作手册》 */
  qmjxjctxx12?: string
  /** 出勤情况 */
  qmjxjctxx13?: string
  /** 是否按规定完成期末拟卷任务 */
  qmjxjctxx14?: string
  /** 备注 */
  qmjxjctxx15?: string
}

/**
 * 教学检查响应数据
 */
export interface TeachingCheckResponse {
  /** 状态 */
  status: number
  /** 检查数据 */
  data: TeachingCheckData
  /** 记录ID */
  id: string
}

/**
 * 教师任务考勤查询参数
 */
export interface TaskAttendanceQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方式 */
  sortOrder?: string
  /** 考勤状态筛选 (not0表示非0状态) */
  kqzt?: string
  /** 班级名称 */
  ssbjmc?: string
  /** 学生学号 */
  xsxh?: string
  /** 学生姓名 */
  xsxm?: string
  /** 上课日期 */
  skrq?: string
  /** 考勤时间 */
  kqsj?: string
}

/**
 * 教师任务考勤项
 */
export interface TaskAttendanceItem {
  /** 考勤记录ID */
  id: number
  /** 考勤数据类型 */
  kqsjlx: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 教学任务ID */
  jxrwid: number
  /** 课程名称 */
  kcmc: string
  /** 周次 */
  zc: number
  /** 上课日期 */
  skrq: string
  /** 考勤开始时间 */
  kqkssj: string
  /** 考勤结束时间 */
  kqjssj: string
  /** 节次显示 */
  jcshow: string
  /** 上课计划ID */
  skjhid: number
  /** 课时 */
  ks: number
  /** 学院代码 */
  ssxy: string
  /** 系部代码 */
  ssxb: string
  /** 系部名称 */
  ssxbmc: string
  /** 班级代码 */
  ssbj: string
  /** 班级名称 */
  ssbjmc: string
  /** 学生学号 */
  xsxh: string
  /** 座位号 */
  zwh: number
  /** 学生姓名 */
  xsxm: string
  /** 考勤状态 */
  kqzt: number
  /** 考勤其他状态 */
  kqqtzt: number | null
  /** 考勤提交人 */
  kqtjry: string
  /** 考勤提交人姓名 */
  kqtjryxm: string
  /** 考勤时间 */
  kqsj: string
  /** 考勤其他时间 */
  kqqtsj: string | null
  /** 管理请假 */
  glqj: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 临时上报 */
  lysb: number
  /** 是否及时考勤 */
  sfjcks: number
  /** 上课开始时间 */
  skkssj: string
  /** 上课结束时间 */
  skjssj: string
  /** 星期 */
  xqs: number
  /** 节次 */
  jc: string
  /** 上课内容 */
  sknl: string
  /** 上课方式 */
  skfs: string
  /** 上课方式名称 */
  skfsmc: string
  /** 上课场地类型 */
  skcdlx: string
  /** 上课场地代码 */
  skcddm: string
  /** 上课场地名称 */
  skcdmc: string
  /** 上课教师 */
  skjs: string
  /** 上课教师姓名 */
  skjsxm: string
  /** 上课班级 */
  skbj: string
  /** 上课班级名称 */
  skbjmc: string
  /** 课程课时 */
  kcks: number
  /** 性质 */
  xz: number
}

/**
 * 教师任务考勤响应
 */
export interface TaskAttendanceResponse {
  /** 考勤列表 */
  items: TaskAttendanceItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
}

/**
 * 获取授课安排场地列表请求参数
 */
export interface GetArrangeSiteListRequest {
  /** 教学任务ID */
  jxrwid: number
  /** 授课计划ID */
  skjhid: number
}

/**
 * 授课安排场地信息
 */
export interface ArrangeSiteInfo {
  /** 使用场地代码 */
  sycddm: string
  /** 使用场地名称 */
  sycdmc: string
  /** 所属校区名称 */
  ssxqmc: string
  /** 所属建筑楼名称 */
  ssjzlmc: string | null
  /** 班级容量 */
  bjrl: number
  /** 层号 */
  ch: number
  /** 门牌号 */
  mph: string
  /** 授课计划详细节次 */
  skjhxxjc: any[]
  /** 是否显示单选按钮 */
  showRadio: boolean
}

/**
 * 获取授课安排场地列表响应数据
 */
export interface GetArrangeSiteListResponse {
  /** 跨列数 */
  colspan: number
  /** 上课日期 */
  skrq: string
  /** 星期显示 */
  xqs: string
  /** 节次显示 */
  jcshow: string
  /** 申请场地信息数组 */
  sqcdxxarr: Record<string, ArrangeSiteInfo>
  /** 申请其他场地信息数组 */
  sqqtcdxxarr: Record<string, ArrangeSiteInfo>
}

/**
 * 安排授课场地请求参数
 */
export interface ArrangeTeachingSiteRequest {
  /** 选择的场地代码 */
  selectcd: string
  /** 授课计划ID */
  skjhid: number
  /** 教学任务ID */
  jxrwid: number
  /** 授课场地类型 */
  skcdlx: string
  /** 操作信息（固定值：1） */
  opxx?: string
  /** 合并联动（固定值：1） */
  hbld?: string
  /** 授课内容（固定值：空字符串） */
  sknl?: string
}

/**
 * 安排授课场地响应数据
 */
export interface ArrangeTeachingSiteResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 响应数据 */
  data: boolean
}

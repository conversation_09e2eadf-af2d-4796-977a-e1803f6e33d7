<template>
  <view class="form-item" :class="itemClass">
    <slot />
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

// Props定义
interface Props {
  /** 底部间距大小 */
  marginBottom?: 'none' | 'small' | 'medium' | 'large'
  /** 自定义样式类 */
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  marginBottom: 'medium',
  customClass: '',
})

// 计算样式类
const itemClass = computed(() => {
  const classes = []

  // 底部间距
  switch (props.marginBottom) {
    case 'none':
      break
    case 'small':
      classes.push('mb-[12rpx]')
      break
    case 'medium':
      classes.push('mb-[20rpx]')
      break
    case 'large':
      classes.push('mb-[32rpx]')
      break
  }

  // 自定义样式类
  if (props.customClass) {
    classes.push(props.customClass)
  }

  return classes
})
</script>

<style lang="scss" scoped>
.form-item {
  // 通用表单项样式
}
</style>
